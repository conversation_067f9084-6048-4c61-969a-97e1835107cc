{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "interactive", "[java]": {"editor.formatOnSave": true, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.defaultFormatter": "Oracle.oracle-java", "editor.codeActionsOnSave": {"source.organizeImports": "never"}}, "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx4G -Xms100m -Xlog:disable"}