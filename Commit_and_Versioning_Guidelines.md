# Commit Message and Versioning Standards

本文定義此 Java Maven 專案的 **Commit 訊息規範** 與 **版本控管政策**。

---

## 1. Commit 訊息規範

我們遵循 **Conventional Commits** 規範，使提交紀錄結構清晰、便於閱讀與自動化處理。

### 1.1 格式

```text
<類型>(<範圍>): <簡短描述>

<選填：詳細描述>

<選填：關聯事項或破壞性變更說明>
```

### 1.2 類型（Type）

| 類型       | 說明                  |
| -------- | ------------------- |
| feat     | 新增功能                |
| fix      | 錯誤修正                |
| chore    | 維護作業（建置工具、依賴或設定檔更新） |
| refactor | 程式碼重構（不影響功能或修正）     |
| docs     | 文件更新                |
| test     | 新增或更新測試             |
| perf     | 效能優化                |
| style    | 程式碼格式調整（不影響邏輯）      |

### 1.3 範圍（Scope）


範圍標示此次變更影響的模組，例如：

- `common-core`
- `common-datasource`
- `common-audit`

*(建議填寫，以提升清晰度)*

### 1.4 簡短描述（Subject）

- 使用祈使句（例：「新增」、「修正」，而非「新增了」、「修正了」）
- 限 50 字以內
- 不要以句號 (`.`) 結尾

### 1.5 範例

```text
feat(common-datasource): 支援動態資料來源切換
fix(common-core): 修正 ResponseBuilder 錯誤碼解析問題
chore: 升級 Spring Boot 至 3.4.4 版本
```

### 1.6 其他補充（Footer，可選）

- 關聯 Issue
- 註記破壞性變更（BREAKING CHANGE）

```text
BREAKING CHANGE: 重構資料來源切換邏輯

Issue: #123
```

---

## 2. 版本控管政策

我們遵循 **Semantic Versioning 2.0.0（語義化版本控管）**。

### 2.1 版本格式

```text
主版本號(MAJOR).次版本號(MINOR).修訂號(PATCH)
```

- **主版本號（MAJOR）**：當進行不相容的 API 修改
- **次版本號（MINOR）**：新增功能且保持向下相容
- **修訂號（PATCH）**：錯誤修正且保持向下相容

### 2.2 版本範例

| 版本    | 說明          |
| ----- | ----------- |
| 1.0.0 | 第一版穩定釋出     |
| 1.1.0 | 新增功能（向下相容）  |
| 1.1.1 | 修正小錯誤（向下相容） |
| 2.0.0 | 破壞性變更       |

### 2.3 Git 標籤操作（Tagging）

每次釋出需打標籤（Tag）：

```bash
git tag -a v1.2.0 -m "釋出版本 1.2.0"
git push origin v1.2.0
```

### 2.4 初始開發版（0.0.X 版號使用建議）

- 當專案處於早期開發階段（功能設計未定型），建議版本從 `0.0.1-SNAPSHOT` 開始。
- `0.x.x` 表示 API 不穩定，隨時可能有破壞性變更，開發者可彈性調整。
- 使用 `-SNAPSHOT` 標記開發進行中，建置時會自動更新版本內容。
- 當專案結構穩定且可以提供向下相容保證後，升級至 `1.0.0` 正式版。

#### 2.4.1 common-base 實際範例

目前 common-base 初期設定：

```xml
<version>0.0.1-SNAPSHOT</version>
```

未來發佈正式版時，將版本號升級為 `1.0.0` 並移除 `-SNAPSHOT`。

---

## 3. Git 分支策略

採用簡化版 Git Flow 流程：

| 分支名稱       | 目的          |
| ---------- | ----------- |
| main       | 穩定版（正式釋出版本） |
| develop    | 開發主線        |
| feature/\* | 新功能開發分支     |
| hotfix/\*  | 緊急修正分支      |
| release/\* | 釋出前測試與調整用分支 |

典型流程示意：

```text
feature/* → develop → release/* → main
hotfix/* → main → develop
```

---

## 4. Maven 專案版本管理

- 所有子模組（Module）必須繼承父 POM 的版本。
- 外部依賴版本統一集中管理（建議使用 `common-bom` 模組）。
- 版本升級時，僅需於父專案 POM 中統一調整。

父層 POM 範例：

```xml
<parent>
    <groupId>com.nanshan</groupId>
    <artifactId>common-base</artifactId>
    <version>0.0.1-SNAPSHOT</version>
</parent>
```

---

# END

