# IDE 
.idea/
*.iml
*.ipr
*.iws
out/
target/
.build/
bin/
gen/

# Eclipse 
.settings/
.metadata/
.classpath
.project
*.tmp
*.bak
*.swp
*.swo
*.log
*.tmp.*
*.backup
*.stackdump
.recommenders/
 
# NetBeans 
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
 
# Maven 
mvnw
mvnw.cmd
.mvn/wrapper/
target/
!target/*.jar # 如果你需要保留生成的 jar 文件，可以取消這一行的!
dependency-reduced-pom.xml
.build/reports/binary/*
 
# Gradle 
.gradle/
build/
.gradle.kts
!build/libs/*.jar # 如果你需要保留 Gradle 构建的 jar 文件，可以取消這一行的!
 
# IntelliJ IDEA
.idea/workspace.xml
.idea/tasks.xml
.idea/dictionaries
.idea/libraries
.idea/modules.xml
.idea/vcs.xml
.idea/inspectionProfiles/
.idea/misc.xml
.idea/projectCodeStyle.xml
.idea/uiDesigner.xml
*.idea_modules/
 
# Java 
*.class
*.war
*.ear
*.sar
*.rar
*.codemodel
*.hprof
hs_err_pid*
javacardcap.*
 
# JUnit 
*.junit/
 
# 其他操作系统特定文件
.DS_Store
thumbs.db
ehthumbs.db
Icon?
._*
 
# Local 
local.properties
*.properties.local
.env.local
 
# 日志文件
logs/
*.log
 
# other
.vscode/
*.sublime-project
*.sublime-workspace
# 忽略 H2 產生的本地檔案
*.mv.db
*.trace.db
*.h2.db