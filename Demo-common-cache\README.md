# Demo Common Cache

## 🚀 快速開始

### 1. 編譯和運行

```bash
# 編譯專案
mvn clean compile

# 運行應用
mvn spring-boot:run

# 或者打包後運行
mvn clean package
java -jar target/demo-common-cache-1.0.0-SNAPSHOT.jar
```

### 2. 驗證運行

```bash
# 檢查應用狀態
curl http://localhost:8081/actuator/health

# 檢查組件狀態
curl http://localhost:8081/config/enabled-components

# 運行完整示範
curl http://localhost:8081/demo/all
```



## 🧪 測試指南

### 1. 基本功能測試

```bash
# 測試 Session 管理
curl http://localhost:8081/demo/session

# 測試快取管理
curl http://localhost:8081/demo/cache

# 測試分散式鎖
curl http://localhost:8081/demo/lock

# 測試清理功能
curl http://localhost:8081/demo/cleaner

# 測試協作功能
curl http://localhost:8081/collaboration/user-session

# 測試完整功能示範
curl http://localhost:8081/demo/all
```

### 2. 詳細功能測試

```bash
# Session 詳細測試
curl -X POST http://localhost:8081/demo/session/create

# 快取詳細測試
curl http://localhost:8081/demo/cache/basic
curl http://localhost:8081/demo/cache/batch
curl http://localhost:8081/demo/cache/compute
curl http://localhost:8081/demo/cache/stats

# 分散式鎖詳細測試
curl http://localhost:8081/demo/lock/readwrite
curl http://localhost:8081/demo/lock/execute
curl http://localhost:8081/demo/lock/concurrent
curl http://localhost:8081/demo/lock/status

# 清理功能詳細測試
curl http://localhost:8081/demo/cleaner/manual
curl http://localhost:8081/demo/cleaner/categorized
curl http://localhost:8081/demo/cleaner/pattern
curl http://localhost:8081/demo/cleaner/ttl
curl http://localhost:8081/demo/cleaner/stats
```

### 3. 協作功能測試

```bash
# 用戶 Session 協作
curl http://localhost:8081/collaboration/user-session

# API 請求處理
curl -H "Authorization: Bearer your-jwt-token" http://localhost:8081/collaboration/api-request

# 用戶資料管理
curl -X POST -H "Content-Type: application/json" -d '{"userId":"user001","name":"測試用戶"}' http://localhost:8081/collaboration/user-data

# 批量用戶操作
curl -X POST -H "Content-Type: application/json" -d '{"userIds":["user001","user002"]}' http://localhost:8081/collaboration/batch-users

# 端到端流程
curl -X POST http://localhost:8081/collaboration/end-to-end-demo
```

### 4. 系統監控測試

```bash
# 應用健康檢查
curl http://localhost:8081/actuator/health

# 組件狀態檢查
curl http://localhost:8081/config/enabled-components

# 配置資訊查看
curl http://localhost:8081/config/cache-properties

# 環境資訊查看
curl http://localhost:8081/config/environment
```

### 5. Redis 診斷測試

```bash
# 完整 Redis 診斷
curl http://localhost:8081/diagnostic/redis/full

# 基本連接測試
curl http://localhost:8081/diagnostic/redis/connection

# PubSub 功能測試
curl http://localhost:8081/diagnostic/redis/pubsub

# 分散式鎖測試
curl http://localhost:8081/diagnostic/redis/lock

# 強制解鎖測試
curl http://localhost:8081/diagnostic/redis/force-unlock

# 連接配置資訊
curl http://localhost:8081/diagnostic/redis/info

# Redisson 命令監控
curl http://localhost:8081/diagnostic/redis/command-monitor
```

## 📚 使用範例

### 快速體驗

```bash
# 1. 啟動應用
mvn spring-boot:run

# 2. 檢查應用狀態
curl http://localhost:8081/actuator/health

# 3. 運行完整示範
curl http://localhost:8081/demo/all

# 4. 檢查 Redis 診斷
curl http://localhost:8081/diagnostic/redis/full
```

### 完整測試流程

```bash
# 步驟 1: 系統檢查
curl http://localhost:8081/config/enabled-components
curl http://localhost:8081/config/environment

# 步驟 2: 功能測試
curl http://localhost:8081/demo/session
curl http://localhost:8081/demo/cache
curl http://localhost:8081/demo/lock
curl http://localhost:8081/demo/cleaner

# 步驟 3: Redis 診斷
curl http://localhost:8081/diagnostic/redis/connection
curl http://localhost:8081/diagnostic/redis/info

# 步驟 4: 協作功能測試
curl http://localhost:8081/collaboration/user-session
```