package com.nanshan.demo.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Demo 應用配置屬性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "demo")
public class DemoProperties {

    /**
     * JWT 配置
     */
    private Jwt jwt = new Jwt();

    /**
     * 應用配置
     */
    private App app = new App();

    @Data
    public static class Jwt {

        /**
         * JWT 密鑰
         */
        private String secret = "demo-secret-key-for-jwt-token-generation-must-be-at-least-256-bits";

        /**
         * JWT 過期時間（秒）
         */
        private long expiration = 3600;

        /**
         * JWT 發行者
         */
        private String issuer = "demo-common-cache";
    }

    @Data
    public static class App {

        /**
         * 應用名稱
         */
        private String name = "Demo Common Cache";

        /**
         * 應用版本
         */
        private String version = "1.0.0-SNAPSHOT";

        /**
         * 應用描述
         */
        private String description = "展示 common-cache 最新功能的示範應用";
    }
}
