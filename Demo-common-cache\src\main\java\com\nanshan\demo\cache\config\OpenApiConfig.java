package com.nanshan.demo.cache.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.servers.Server;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * OpenAPI (Swagger) 配置
 * 
 * 提供 API 文檔和測試介面
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
public class OpenApiConfig {

    @Value("${server.port:8084}")
    private String serverPort;

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(apiInfo())
                .servers(List.of(
                        new Server()
                                .url("http://localhost:" + serverPort)
                                .description("本地開發環境"),
                        new Server()
                                .url("https://demo-api.nanshan.com")
                                .description("演示環境")
                ));
    }

    private Info apiInfo() {
        return new Info()
                .title("Demo Common Cache API")
                .description("""
                        ## 🎯 Demo Common Cache API 文檔
                        
                        這是一個完整的 REST API 示範，展示 `common-cache` 框架的所有功能。
                        
                        ### 📚 主要功能模組
                        
                        - **快取管理**: 基本 CRUD、TTL 管理、批次操作
                        - **會話管理**: JWT 會話、自動續期、多索引查詢  
                        - **分散式鎖**: 公平鎖、讀寫鎖、鎖狀態監控
                        - **資料清理**: 過期清理、批次清理、統計報告
                        - **系統監控**: 健康檢查、統計資訊、效能指標
                        
                        ### 🚀 快速開始
                        
                        1. 確保 Redis 服務運行在 `localhost:6379`
                        2. 使用下方的 API 端點進行測試
                        3. 查看 `/actuator/health` 檢查系統狀態
                        
                        ### 📖 更多資訊
                        
                        - [GitHub Repository](https://github.com/your-org/Demo-common-cache)
                        - [Common Cache 文檔](https://github.com/your-org/common-cache)
                        """)
                .version("1.0.0-SNAPSHOT")
                .contact(new Contact()
                        .name("Nanshan Team")
                        .email("<EMAIL>")
                        .url("https://nanshan.com"))
                .license(new License()
                        .name("MIT License")
                        .url("https://opensource.org/licenses/MIT"));
    }
}
