package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.CacheDemoService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 快取管理示範控制器
 *
 * 提供快取管理功能的 REST API 示範
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/cache")
@RequiredArgsConstructor
public class CacheDemoController {

    private final CacheDemoService cacheDemoService;

    /**
     * 快取管理完整示範
     *
     * @return 示範結果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> cacheDemo() {
        log.info("開始執行快取管理完整示範");

        Map<String, Object> result = new HashMap<>();
        Map<String, Object> steps = new HashMap<>();

        try {
            // 步驟 1: 基本快取操作
            log.info("步驟 1: 基本快取操作示範");
            Map<String, Object> basicResult = cacheDemoService.basicCacheDemo();
            steps.put("step1_basic_operations", basicResult);

            // 步驟 2: 批量快取操作
            log.info("步驟 2: 批量快取操作示範");
            Map<String, Object> batchResult = cacheDemoService.batchCacheDemo();
            steps.put("step2_batch_operations", batchResult);

            // 步驟 3: 快取計算操作 (getOrCompute)
            log.info("步驟 3: 快取計算操作示範");
            Map<String, Object> computeResult = cacheDemoService.computeCacheDemo();
            steps.put("step3_compute_operations", computeResult);

            // 步驟 4: 快取統計資訊
            log.info("步驟 4: 快取統計資訊示範");
            Map<String, Object> statsResult = cacheDemoService.cacheStatsDemo();
            steps.put("step4_cache_stats", statsResult);

            // 檢查所有步驟是否成功
            boolean allSuccess = steps.values().stream()
                    .allMatch(step -> (Boolean) ((Map<String, Object>) step).get("success"));

            // 返回結果
            result.put("success", allSuccess);
            result.put("message", allSuccess ? "快取管理示範執行完成" : "快取管理示範部分失敗");
            result.put("description", "展示了快取的基本操作、批量操作、計算操作和統計功能");
            result.put("steps", steps);

            log.info("快取管理完整示範執行完成，成功: {}", allSuccess);

        } catch (Exception e) {
            log.error("快取管理示範執行失敗", e);

            result.put("success", false);
            result.put("message", "快取管理示範執行失敗: " + e.getMessage());
            result.put("steps", steps);

            return ResponseEntity.internalServerError().body(result);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 基本快取操作示範
     *
     * @return 示範結果
     */
    @GetMapping("/basic")
    public ResponseEntity<Map<String, Object>> basicCacheDemo() {
        log.info("執行基本快取操作示範");

        Map<String, Object> result = cacheDemoService.basicCacheDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 批量快取操作示範
     *
     * @return 示範結果
     */
    @GetMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchCacheDemo() {
        log.info("執行批量快取操作示範");

        Map<String, Object> result = cacheDemoService.batchCacheDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 快取計算操作示範
     *
     * @return 示範結果
     */
    @GetMapping("/compute")
    public ResponseEntity<Map<String, Object>> computeCacheDemo() {
        log.info("執行快取計算操作示範");

        Map<String, Object> result = cacheDemoService.computeCacheDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 快取統計資訊示範
     *
     * @return 示範結果
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> cacheStatsDemo() {
        log.info("執行快取統計資訊示範");

        Map<String, Object> result = cacheDemoService.cacheStatsDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
