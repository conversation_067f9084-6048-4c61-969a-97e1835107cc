package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.CleanerDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 清理器示範控制器
 * 
 * 提供清理器功能的 REST API 示範
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/cleaner")
@RequiredArgsConstructor
public class CleanerDemoController {
    
    private final CleanerDemoService cleanerDemoService;
    
    /**
     * 清理器完整示範
     * 
     * @return 示範結果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> cleanerDemo() {
        log.info("開始執行清理器完整示範");
        
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> steps = new HashMap<>();
        
        try {
            // 步驟 1: 手動清理示範
            log.info("步驟 1: 手動清理示範");
            Map<String, Object> manualResult = cleanerDemoService.manualCleanupDemo();
            steps.put("step1_manual_cleanup", manualResult);
            
            // 步驟 2: 分類清理示範
            log.info("步驟 2: 分類清理示範");
            Map<String, Object> categorizedResult = cleanerDemoService.categorizedCleanupDemo();
            steps.put("step2_categorized_cleanup", categorizedResult);
            
            // 步驟 3: 模式刪除示範
            log.info("步驟 3: 模式刪除示範");
            Map<String, Object> patternResult = cleanerDemoService.patternDeleteDemo();
            steps.put("step3_pattern_delete", patternResult);
            
            // 步驟 4: TTL 管理示範
            log.info("步驟 4: TTL 管理示範");
            Map<String, Object> ttlResult = cleanerDemoService.ttlManagementDemo();
            steps.put("step4_ttl_management", ttlResult);
            
            // 步驟 5: 清理統計示範
            log.info("步驟 5: 清理統計示範");
            Map<String, Object> statsResult = cleanerDemoService.cleanupStatsDemo();
            steps.put("step5_cleanup_stats", statsResult);
            
            // 檢查所有步驟是否成功
            boolean allSuccess = steps.values().stream()
                    .allMatch(step -> (Boolean) ((Map<String, Object>) step).get("success"));
            
            // 返回結果
            result.put("success", allSuccess);
            result.put("message", allSuccess ? "清理器示範執行完成" : "清理器示範部分失敗");
            result.put("description", "展示了手動清理、分類清理、模式刪除、TTL 管理和清理統計功能");
            result.put("steps", steps);
            
            log.info("清理器完整示範執行完成，成功: {}", allSuccess);
            
        } catch (Exception e) {
            log.error("清理器示範執行失敗", e);
            
            result.put("success", false);
            result.put("message", "清理器示範執行失敗: " + e.getMessage());
            result.put("steps", steps);
            
            return ResponseEntity.internalServerError().body(result);
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 手動清理示範
     * 
     * @return 示範結果
     */
    @GetMapping("/manual")
    public ResponseEntity<Map<String, Object>> manualCleanupDemo() {
        log.info("執行手動清理示範");
        
        Map<String, Object> result = cleanerDemoService.manualCleanupDemo();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 分類清理示範
     * 
     * @return 示範結果
     */
    @GetMapping("/categorized")
    public ResponseEntity<Map<String, Object>> categorizedCleanupDemo() {
        log.info("執行分類清理示範");
        
        Map<String, Object> result = cleanerDemoService.categorizedCleanupDemo();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 模式刪除示範
     * 
     * @return 示範結果
     */
    @GetMapping("/pattern")
    public ResponseEntity<Map<String, Object>> patternDeleteDemo() {
        log.info("執行模式刪除示範");
        
        Map<String, Object> result = cleanerDemoService.patternDeleteDemo();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * TTL 管理示範
     * 
     * @return 示範結果
     */
    @GetMapping("/ttl")
    public ResponseEntity<Map<String, Object>> ttlManagementDemo() {
        log.info("執行 TTL 管理示範");
        
        Map<String, Object> result = cleanerDemoService.ttlManagementDemo();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
    
    /**
     * 清理統計示範
     * 
     * @return 示範結果
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> cleanupStatsDemo() {
        log.info("執行清理統計示範");
        
        Map<String, Object> result = cleanerDemoService.cleanupStatsDemo();
        
        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
