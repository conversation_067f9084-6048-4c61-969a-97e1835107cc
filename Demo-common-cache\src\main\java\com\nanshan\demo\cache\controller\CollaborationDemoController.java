package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.model.DemoUser;
import com.nanshan.demo.cache.service.CollaborationDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * SessionManager 和 CacheManager 協同工作示範控制器
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/collaboration")
@RequiredArgsConstructor
public class CollaborationDemoController {
    
    private final CollaborationDemoService collaborationDemoService;
    
    /**
     * 完整登入流程示範
     * 
     * @param request 登入請求
     * @param httpRequest HTTP 請求
     * @return 登入結果
     */
    @PostMapping("/complete-login")
    public ResponseEntity<Map<String, Object>> completeLogin(
            @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("收到完整登入請求: username={}, clientId={}", request.getUsername(), request.getClientId());
        
        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");
        
        Map<String, Object> result = collaborationDemoService.performCompleteLogin(
            request.getUsername(),
            request.getClientId(),
            ipAddress,
            userAgent
        );
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * API 請求處理示範
     * 
     * @param authHeader Authorization Header
     * @return 處理結果
     */
    @GetMapping("/api-request")
    public ResponseEntity<Map<String, Object>> processApiRequest(
            @RequestHeader("Authorization") String authHeader) {
        
        log.info("收到 API 請求處理示範");
        
        // 提取 JWT Token
        String jwtToken = authHeader.startsWith("Bearer ") ? 
            authHeader.substring(7) : authHeader;
        
        Map<String, Object> result = collaborationDemoService.processApiRequest(jwtToken);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 用戶資料更新示範
     * 
     * @param userId 用戶 ID
     * @param request 更新請求
     * @return 更新結果
     */
    @PutMapping("/update-user/{userId}")
    public ResponseEntity<Map<String, Object>> updateUser(
            @PathVariable String userId,
            @RequestBody UpdateUserRequest request) {
        
        log.info("收到用戶資料更新請求: userId={}", userId);
        
        // 構建更新的用戶物件
        DemoUser updatedUser = DemoUser.builder()
            .userId(userId)
            .username(request.getUsername())
            .name(request.getName())
            .email(request.getEmail())
            .roles(request.getRoles())
            .permissions(request.getPermissions())
            .department(request.getDepartment())
            .enabled(request.isEnabled())
            .build();
        
        Map<String, Object> result = collaborationDemoService.updateUserData(userId, updatedUser);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 批量用戶操作示範
     * 
     * @param request 批量請求
     * @return 操作結果
     */
    @PostMapping("/batch-users")
    public ResponseEntity<Map<String, Object>> batchUserOperations(@RequestBody BatchUserRequest request) {
        log.info("收到批量用戶操作請求: userIds={}", request.getUserIds());
        
        Map<String, Object> result = collaborationDemoService.batchUserOperations(request.getUserIds());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 端到端流程示範
     * 
     * @return 示範結果
     */
    @PostMapping("/end-to-end-demo")
    public ResponseEntity<Map<String, Object>> endToEndDemo() {
        log.info("收到端到端流程示範請求");
        
        Map<String, Object> result = new java.util.HashMap<>();
        List<Map<String, Object>> steps = new java.util.ArrayList<>();
        
        try {
            // 步驟 1：完整登入
            Map<String, Object> loginResult = collaborationDemoService.performCompleteLogin(
                "admin", "demo-app", "127.0.0.1", "Demo-EndToEnd-Agent");
            steps.add(Map.of("step", "login", "result", loginResult));
            
            if ((Boolean) loginResult.get("success")) {
                String jwtToken = (String) loginResult.get("jwtToken");
                
                // 步驟 2：API 請求處理
                Map<String, Object> apiResult = collaborationDemoService.processApiRequest(jwtToken);
                steps.add(Map.of("step", "apiRequest", "result", apiResult));
                
                // 步驟 3：用戶資料更新
                DemoUser updatedUser = DemoUser.builder()
                    .userId("admin001")
                    .username("admin")
                    .name("系統管理員（已更新）")
                    .email("<EMAIL>")
                    .roles(Arrays.asList("ADMIN", "USER", "SUPER_ADMIN"))
                    .permissions(Arrays.asList("READ", "WRITE", "DELETE", "ADMIN", "SUPER_ADMIN"))
                    .department("IT")
                    .enabled(true)
                    .build();
                
                Map<String, Object> updateResult = collaborationDemoService.updateUserData("admin001", updatedUser);
                steps.add(Map.of("step", "updateUser", "result", updateResult));
                
                // 步驟 4：批量操作
                Map<String, Object> batchResult = collaborationDemoService.batchUserOperations(
                    Arrays.asList("admin001", "user001", "user002"));
                steps.add(Map.of("step", "batchOperations", "result", batchResult));
            }
            
            result.put("success", true);
            result.put("message", "端到端流程示範完成");
            result.put("totalSteps", steps.size());
            result.put("steps", steps);
            
        } catch (Exception e) {
            log.error("端到端流程示範失敗", e);
            result.put("success", false);
            result.put("message", "端到端流程示範失敗: " + e.getMessage());
            result.put("completedSteps", steps.size());
            result.put("steps", steps);
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 獲取客戶端 IP 地址
     * 
     * @param request HTTP 請求
     * @return IP 地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    // 請求 DTO 類別
    
    public static class LoginRequest {
        private String username;
        private String clientId = "demo-app";
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }
    }
    
    public static class UpdateUserRequest {
        private String username;
        private String name;
        private String email;
        private List<String> roles;
        private List<String> permissions;
        private String department;
        private boolean enabled = true;
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public List<String> getRoles() { return roles; }
        public void setRoles(List<String> roles) { this.roles = roles; }
        public List<String> getPermissions() { return permissions; }
        public void setPermissions(List<String> permissions) { this.permissions = permissions; }
        public String getDepartment() { return department; }
        public void setDepartment(String department) { this.department = department; }
        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }
    }
    
    public static class BatchUserRequest {
        private List<String> userIds;
        
        // Getters and Setters
        public List<String> getUserIds() { return userIds; }
        public void setUserIds(List<String> userIds) { this.userIds = userIds; }
    }
}
