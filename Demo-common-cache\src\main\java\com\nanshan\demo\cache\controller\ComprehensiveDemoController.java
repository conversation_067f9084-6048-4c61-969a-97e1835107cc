package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.ComprehensiveDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 綜合示範控制器
 * 
 * 整合所有功能模組的完整使用流程示範
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/all")
@RequiredArgsConstructor
public class ComprehensiveDemoController {
    
    private final ComprehensiveDemoService comprehensiveDemoService;
    
    /**
     * 執行綜合示範
     * 
     * @return 示範結果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> runComprehensiveDemo() {
        log.info("開始執行綜合示範");
        
        Map<String, Object> result = comprehensiveDemoService.runComprehensiveDemo();
        
        if ((Boolean) result.get("success")) {
            log.info("綜合示範執行成功");
            return ResponseEntity.ok(result);
        } else {
            log.error("綜合示範執行失敗: {}", result.get("message"));
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
