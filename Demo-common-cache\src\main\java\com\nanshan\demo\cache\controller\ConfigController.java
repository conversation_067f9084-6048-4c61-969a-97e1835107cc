package com.nanshan.demo.cache.controller;

import com.nanshan.common.cache.config.CommonCacheProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.env.Environment;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 配置檢查控制器
 *
 * 提供配置檢查和元件狀態查詢功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/config")
@RequiredArgsConstructor
public class ConfigController {

    private final CommonCacheProperties cacheProperties;
    private final Environment environment;
    private final ApplicationContext applicationContext;

    /**
     * 檢查啟用的元件
     *
     * @return 元件狀態
     */
    @GetMapping("/enabled-components")
    public ResponseEntity<Map<String, Object>> getEnabledComponents() {
        log.info("檢查啟用的元件");

        Map<String, Object> result = new HashMap<>();

        try {
            // 檢查各個元件是否存在於 Spring 容器中
            Map<String, Boolean> components = new HashMap<>();

            // 檢查 SessionManager（Bean 名稱是方法名）
            components.put("SessionManager", applicationContext.containsBean("sessionManager"));

            // 檢查 CacheManager（Bean 名稱是方法名）
            components.put("CacheManager", applicationContext.containsBean("cacheManager"));

            // 檢查 LockManager（Bean 名稱是方法名）
            components.put("LockManager", applicationContext.containsBean("lockManager"));

            // 檢查 RedisCleaner（Bean 名稱是方法名）
            components.put("RedisCleaner", applicationContext.containsBean("redisCleaner"));

            // 檢查 RedisDataAccessor（Bean 名稱是方法名）
            components.put("RedisDataAccessor", applicationContext.containsBean("redisDataAccessor"));

            result.put("success", true);
            result.put("message", "元件狀態檢查完成");
            result.put("components", components);

            log.info("元件狀態檢查完成: {}", components);

        } catch (Exception e) {
            log.error("元件狀態檢查失敗", e);

            result.put("success", false);
            result.put("message", "元件狀態檢查失敗: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 獲取 Common Cache 配置
     *
     * @return 配置資訊
     */
    @GetMapping("/cache-properties")
    public ResponseEntity<Map<String, Object>> getCacheProperties() {
        log.info("獲取 Common Cache 配置");

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> config = new HashMap<>();

            // 環境保護配置
            config.put("envGuard", Map.of(
                    "enabled", cacheProperties.getEnvGuard().isEnabled(),
                    "productionProfiles", cacheProperties.getEnvGuard().getProductionProfiles(),
                    "dangerousOperationsEnabled", cacheProperties.getEnvGuard().isDangerousOperationsEnabled()
            ));

            // Session 配置
            config.put("session", Map.of(
                    "defaultTtl", 1800, // 暫時硬編碼
                    "keyPrefix", cacheProperties.getSession().getKeyPrefix(),
                    "cleanupInterval", cacheProperties.getSession().getCleanupInterval(),
                    "autoRenewal", cacheProperties.getSession().isAutoRenewal(),
                    "renewalThreshold", cacheProperties.getSession().getRenewalThreshold()
            ));

            // Cache 配置
            config.put("cache", Map.of(
                    "defaultTtl", 3600, // 暫時硬編碼
                    "keyPrefix", cacheProperties.getCache().getKeyPrefix(),
                    "autoRenewal", cacheProperties.getCache().isAutoRenewal(),
                    "renewalThreshold", cacheProperties.getCache().getRenewalThreshold(),
                    "maxSize", cacheProperties.getCache().getMaxSize()
            ));

            // Lock 配置
            config.put("lock", Map.of(
                    "defaultLeaseTime", cacheProperties.getLock().getDefaultLeaseTime(),
                    "defaultWaitTime", cacheProperties.getLock().getDefaultWaitTime(),
                    "keyPrefix", cacheProperties.getLock().getKeyPrefix(),
                    "fairLockEnabled", cacheProperties.getLock().isFairLockEnabled()
            ));

            // Cleaner 配置
            config.put("cleaner", Map.of(
                    "enabled", cacheProperties.getCleaner().isEnabled(),
                    "scheduleInterval", cacheProperties.getCleaner().getScheduleInterval(),
                    "batchSize", cacheProperties.getCleaner().getBatchSize(),
                    "expiredKeyScanCount", cacheProperties.getCleaner().getExpiredKeyScanCount()
            ));

            result.put("success", true);
            result.put("message", "配置資訊獲取完成");
            result.put("config", config);

            log.info("配置資訊獲取完成");

        } catch (Exception e) {
            log.error("配置資訊獲取失敗", e);

            result.put("success", false);
            result.put("message", "配置資訊獲取失敗: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 獲取 Redis 連線配置
     *
     * @return Redis 配置資訊
     */
    @GetMapping("/redis-properties")
    public ResponseEntity<Map<String, Object>> getRedisProperties() {
        log.info("獲取 Redis 連線配置");

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> redisConfig = new HashMap<>();

            // Redis 基本配置
            redisConfig.put("host", environment.getProperty("spring.redis.host", "localhost"));
            redisConfig.put("port", environment.getProperty("spring.redis.port", "6379"));
            redisConfig.put("database", environment.getProperty("spring.redis.database", "0"));
            redisConfig.put("timeout", environment.getProperty("spring.redis.timeout", "3000ms"));

            // 連線池配置
            Map<String, Object> poolConfig = new HashMap<>();
            poolConfig.put("maxActive", environment.getProperty("spring.redis.lettuce.pool.max-active", "8"));
            poolConfig.put("maxIdle", environment.getProperty("spring.redis.lettuce.pool.max-idle", "8"));
            poolConfig.put("minIdle", environment.getProperty("spring.redis.lettuce.pool.min-idle", "0"));
            poolConfig.put("maxWait", environment.getProperty("spring.redis.lettuce.pool.max-wait", "-1ms"));

            redisConfig.put("pool", poolConfig);

            result.put("success", true);
            result.put("message", "Redis 配置資訊獲取完成");
            result.put("redisConfig", redisConfig);

            log.info("Redis 配置資訊獲取完成");

        } catch (Exception e) {
            log.error("Redis 配置資訊獲取失敗", e);

            result.put("success", false);
            result.put("message", "Redis 配置資訊獲取失敗: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 獲取應用程式環境資訊
     *
     * @return 環境資訊
     */
    @GetMapping("/environment")
    public ResponseEntity<Map<String, Object>> getEnvironmentInfo() {
        log.info("獲取應用程式環境資訊");

        Map<String, Object> result = new HashMap<>();

        try {
            Map<String, Object> envInfo = new HashMap<>();

            // 基本環境資訊
            envInfo.put("applicationName", environment.getProperty("spring.application.name", "demo-common-cache"));
            envInfo.put("activeProfiles", environment.getActiveProfiles());
            envInfo.put("defaultProfiles", environment.getDefaultProfiles());
            envInfo.put("serverPort", environment.getProperty("server.port", "8080"));

            // Java 環境資訊
            envInfo.put("javaVersion", System.getProperty("java.version"));
            envInfo.put("javaVendor", System.getProperty("java.vendor"));
            envInfo.put("osName", System.getProperty("os.name"));
            envInfo.put("osVersion", System.getProperty("os.version"));

            result.put("success", true);
            result.put("message", "環境資訊獲取完成");
            result.put("environment", envInfo);

            log.info("環境資訊獲取完成");

        } catch (Exception e) {
            log.error("環境資訊獲取失敗", e);

            result.put("success", false);
            result.put("message", "環境資訊獲取失敗: " + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
