package com.nanshan.demo.cache.controller;

import com.nanshan.common.cache.service.RedisDiagnosticService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * Redis 診斷控制器
 *
 * 提供 Redis 連接和功能診斷的 API 端點
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/diagnostic")
@RequiredArgsConstructor
@Tag(name = "Redis 診斷", description = "Redis 連接和功能診斷 API")
public class DiagnosticController {

    private final RedisDiagnosticService diagnosticService;

    @GetMapping("/redis/full")
    @Operation(summary = "完整 Redis 診斷", description = "執行完整的 Redis 連接和功能診斷")
    public ResponseEntity<Map<String, Object>> runFullDiagnostic() {
        log.info("執行完整 Redis 診斷");

        try {
            Map<String, Object> results = diagnosticService.runFullDiagnostic();
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            log.error("Redis 診斷失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "診斷執行失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }

    @GetMapping("/redis/connection")
    @Operation(summary = "基本連接測試", description = "測試基本的 Redis 連接功能")
    public ResponseEntity<Map<String, Object>> testBasicConnection() {
        log.info("執行基本連接測試");

        try {
            Map<String, Object> result = diagnosticService.testBasicConnection();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("基本連接測試失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "基本連接測試失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }

    @GetMapping("/redis/pubsub")
    @Operation(summary = "PubSub 功能測試", description = "測試 Redis PubSub 功能")
    public ResponseEntity<Map<String, Object>> testPubSubConnection() {
        log.info("執行 PubSub 功能測試");

        try {
            Map<String, Object> result = diagnosticService.testPubSubConnection();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("PubSub 功能測試失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "PubSub 功能測試失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }

    @GetMapping("/redis/lock")
    @Operation(summary = "分散式鎖測試", description = "測試分散式鎖功能")
    public ResponseEntity<Map<String, Object>> testDistributedLock() {
        log.info("執行分散式鎖測試");

        try {
            Map<String, Object> result = diagnosticService.testDistributedLock();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("分散式鎖測試失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "分散式鎖測試失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }

    @GetMapping("/redis/force-unlock")
    @Operation(summary = "強制解鎖測試", description = "測試強制解鎖功能")
    public ResponseEntity<Map<String, Object>> testForceUnlock() {
        log.info("執行強制解鎖測試");

        try {
            Map<String, Object> result = diagnosticService.testForceUnlock();
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("強制解鎖測試失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "強制解鎖測試失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }

    @GetMapping("/redis/info")
    @Operation(summary = "Redis 連接信息", description = "獲取當前 Redis 連接配置信息")
    public ResponseEntity<Map<String, Object>> getConnectionInfo() {
        log.info("獲取 Redis 連接信息");

        try {
            Map<String, Object> info = diagnosticService.getConnectionInfo();
            return ResponseEntity.ok(info);
        } catch (Exception e) {
            log.error("獲取連接信息失敗", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                            "success", false,
                            "message", "獲取連接信息失敗: " + e.getMessage(),
                            "error", e.getClass().getSimpleName()
                    ));
        }
    }
}
