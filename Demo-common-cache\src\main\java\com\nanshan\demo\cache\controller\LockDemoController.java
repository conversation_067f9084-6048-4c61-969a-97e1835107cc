package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.LockDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 分散式鎖示範控制器
 *
 * 提供分散式鎖功能的 REST API 示範
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/lock")
@RequiredArgsConstructor
public class LockDemoController {

    private final LockDemoService lockDemoService;

    /**
     * 分散式鎖完整示範
     *
     * @return 示範結果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> lockDemo() {
        log.info("開始執行分散式鎖完整示範");

        Map<String, Object> result = new HashMap<>();
        Map<String, Object> steps = new HashMap<>();

        try {
            // 步驟 1: 可重入鎖示範
            log.info("步驟 1: 可重入鎖示範");
            Map<String, Object> reentrantResult = lockDemoService.reentrantLockDemo();
            steps.put("step1_reentrant_lock", reentrantResult);

            // 步驟 2: 公平鎖示範
            log.info("步驟 2: 公平鎖示範");
            Map<String, Object> fairResult = lockDemoService.fairLockDemo();
            steps.put("step2_fair_lock", fairResult);

            // 步驟 3: 讀寫鎖示範
            log.info("步驟 3: 讀寫鎖示範");
            Map<String, Object> readWriteResult = lockDemoService.readWriteLockDemo();
            steps.put("step3_read_write_lock", readWriteResult);

            // 步驟 4: 鎖保護執行示範
            log.info("步驟 4: 鎖保護執行示範");
            Map<String, Object> executeResult = lockDemoService.executeWithLockDemo();
            steps.put("step4_execute_with_lock", executeResult);

            // 步驟 5: 併發鎖測試示範
            log.info("步驟 5: 併發鎖測試示範");
            Map<String, Object> concurrentResult = lockDemoService.concurrentLockDemo();
            steps.put("step5_concurrent_lock", concurrentResult);

            // 步驟 6: 鎖狀態檢查示範
            log.info("步驟 6: 鎖狀態檢查示範");
            Map<String, Object> statusResult = lockDemoService.lockStatusDemo();
            steps.put("step6_lock_status", statusResult);

            // 步驟 7: 強制解鎖示範
            log.info("步驟 7: 強制解鎖示範");
            Map<String, Object> forceUnlockResult = lockDemoService.forceUnlockDemo();
            steps.put("step7_force_unlock", forceUnlockResult);

            // 檢查所有步驟是否成功
            boolean allSuccess = steps.values().stream()
                    .allMatch(step -> (Boolean) ((Map<String, Object>) step).get("success"));

            // 返回結果
            result.put("success", allSuccess);
            result.put("message", allSuccess ? "分散式鎖示範執行完成" : "分散式鎖示範部分失敗");
            result.put("description", "展示了可重入鎖、公平鎖、讀寫鎖、鎖保護執行和併發鎖測試");
            result.put("steps", steps);

            log.info("分散式鎖完整示範執行完成，成功: {}", allSuccess);

        } catch (Exception e) {
            log.error("分散式鎖示範執行失敗", e);

            result.put("success", false);
            result.put("message", "分散式鎖示範執行失敗: " + e.getMessage());
            result.put("steps", steps);

            return ResponseEntity.internalServerError().body(result);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 可重入鎖示範
     *
     * @return 示範結果
     */
    @GetMapping("/reentrant")
    public ResponseEntity<Map<String, Object>> reentrantLockDemo() {
        log.info("執行可重入鎖示範");

        Map<String, Object> result = lockDemoService.reentrantLockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 公平鎖示範
     *
     * @return 示範結果
     */
    @GetMapping("/fair")
    public ResponseEntity<Map<String, Object>> fairLockDemo() {
        log.info("執行公平鎖示範");

        Map<String, Object> result = lockDemoService.fairLockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 讀寫鎖示範
     *
     * @return 示範結果
     */
    @GetMapping("/readwrite")
    public ResponseEntity<Map<String, Object>> readWriteLockDemo() {
        log.info("執行讀寫鎖示範");

        Map<String, Object> result = lockDemoService.readWriteLockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 鎖保護執行示範
     *
     * @return 示範結果
     */
    @GetMapping("/execute")
    public ResponseEntity<Map<String, Object>> executeWithLockDemo() {
        log.info("執行鎖保護執行示範");

        Map<String, Object> result = lockDemoService.executeWithLockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 併發鎖測試示範
     *
     * @return 示範結果
     */
    @GetMapping("/concurrent")
    public ResponseEntity<Map<String, Object>> concurrentLockDemo() {
        log.info("執行併發鎖測試示範");

        Map<String, Object> result = lockDemoService.concurrentLockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 鎖狀態檢查示範
     *
     * @return 示範結果
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> lockStatusDemo() {
        log.info("執行鎖狀態檢查示範");

        Map<String, Object> result = lockDemoService.lockStatusDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 強制解鎖示範
     *
     * @return 示範結果
     */
    @GetMapping("/force-unlock")
    public ResponseEntity<Map<String, Object>> forceUnlockDemo() {
        log.info("執行強制解鎖示範");

        Map<String, Object> result = lockDemoService.forceUnlockDemo();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
