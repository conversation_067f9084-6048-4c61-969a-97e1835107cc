package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.SessionDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * Session 管理示範控制器
 *
 * 提供 Session 管理功能的 REST API 示範
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/session")
@RequiredArgsConstructor
public class SessionDemoController {

    private final SessionDemoService sessionDemoService;

    /**
     * Session 管理完整示範
     *
     * @return 示範結果
     */
    @GetMapping
    public ResponseEntity<Map<String, Object>> sessionDemo() {
        log.info("開始執行 Session 管理完整示範");

        Map<String, Object> result = new HashMap<>();
        Map<String, Object> steps = new HashMap<>();

        try {
            // 步驟 1: 建立 Session
            log.info("步驟 1: 建立示範 Session");
            Map<String, Object> createResult = sessionDemoService.createDemoSession();
            steps.put("step1_create", createResult);

            if (!(Boolean) createResult.get("success")) {
                throw new RuntimeException("建立 Session 失敗");
            }

            String jwtId = (String) createResult.get("jwtId");

            // 步驟 2: 查詢 Session
            log.info("步驟 2: 查詢 Session - {}", jwtId);
            Map<String, Object> getResult = sessionDemoService.getSession(jwtId);
            steps.put("step2_get", getResult);

            // 步驟 3: 續期 Session
            log.info("步驟 3: 續期 Session - {}", jwtId);
            Map<String, Object> renewResult = sessionDemoService.renewSession(jwtId, 3600);
            steps.put("step3_renew", renewResult);

            // 步驟 4: 再次查詢 Session (驗證續期)
            log.info("步驟 4: 再次查詢 Session (驗證續期) - {}", jwtId);
            Map<String, Object> getAfterRenewResult = sessionDemoService.getSession(jwtId);
            steps.put("step4_get_after_renew", getAfterRenewResult);

            // 步驟 5: 獲取統計資訊
            log.info("步驟 5: 獲取 Session 統計資訊");
            Map<String, Object> statsResult = sessionDemoService.getSessionStats();
            steps.put("step5_stats", statsResult);

            // 步驟 6: 刪除 Session
            log.info("步驟 6: 刪除 Session - {}", jwtId);
            Map<String, Object> deleteResult = sessionDemoService.deleteSession(jwtId);
            steps.put("step6_delete", deleteResult);

            // 步驟 7: 驗證刪除 (再次查詢)
            log.info("步驟 7: 驗證刪除 (再次查詢) - {}", jwtId);
            Map<String, Object> getAfterDeleteResult = sessionDemoService.getSession(jwtId);
            steps.put("step7_get_after_delete", getAfterDeleteResult);

            // 返回成功結果
            result.put("success", true);
            result.put("message", "Session 管理示範執行完成");
            result.put("description", "展示了 Session 的建立、查詢、續期、統計和刪除功能");
            result.put("steps", steps);

            log.info("Session 管理完整示範執行成功");

        } catch (Exception e) {
            log.error("Session 管理示範執行失敗", e);

            result.put("success", false);
            result.put("message", "Session 管理示範執行失敗: " + e.getMessage());
            result.put("steps", steps);

            return ResponseEntity.internalServerError().body(result);
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 建立 Session
     *
     * @return 建立結果
     */
    @PostMapping("/create")
    public ResponseEntity<Map<String, Object>> createSession() {
        log.info("建立新的示範 Session");

        Map<String, Object> result = sessionDemoService.createDemoSession();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }

    /**
     * 查詢 Session
     *
     * @param jwtId JWT ID
     * @return 查詢結果
     */
    @GetMapping("/{jwtId}")
    public ResponseEntity<Map<String, Object>> getSession(@PathVariable String jwtId) {
        log.info("查詢 Session: {}", jwtId);

        Map<String, Object> result = sessionDemoService.getSession(jwtId);

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 續期 Session
     *
     * @param jwtId JWT ID
     * @param ttlSeconds TTL 秒數
     * @return 續期結果
     */
    @PutMapping("/{jwtId}/renew")
    public ResponseEntity<Map<String, Object>> renewSession(
            @PathVariable String jwtId,
            @RequestParam(defaultValue = "3600") long ttlSeconds) {
        log.info("續期 Session: {}, TTL: {}秒", jwtId, ttlSeconds);

        Map<String, Object> result = sessionDemoService.renewSession(jwtId, ttlSeconds);

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 刪除 Session
     *
     * @param jwtId JWT ID
     * @return 刪除結果
     */
    @DeleteMapping("/{jwtId}")
    public ResponseEntity<Map<String, Object>> deleteSession(@PathVariable String jwtId) {
        log.info("刪除 Session: {}", jwtId);

        Map<String, Object> result = sessionDemoService.deleteSession(jwtId);

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.badRequest().body(result);
        }
    }

    /**
     * 獲取 Session 統計資訊
     *
     * @return 統計資訊
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getSessionStats() {
        log.info("獲取 Session 統計資訊");

        Map<String, Object> result = sessionDemoService.getSessionStats();

        if ((Boolean) result.get("success")) {
            return ResponseEntity.ok(result);
        } else {
            return ResponseEntity.internalServerError().body(result);
        }
    }
}
