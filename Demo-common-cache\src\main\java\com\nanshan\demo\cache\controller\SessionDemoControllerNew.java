package com.nanshan.demo.cache.controller;

import com.nanshan.demo.cache.service.SessionDemoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Session 示範控制器 - 展示最新的 SessionManager 功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/demo/session")
@RequiredArgsConstructor
public class SessionDemoControllerNew {
    
    private final SessionDemoService sessionDemoService;
    
    /**
     * 用戶登入示範
     * 
     * @param request 登入請求
     * @param httpRequest HTTP 請求
     * @return 登入結果
     */
    @PostMapping("/login")
    public ResponseEntity<Map<String, Object>> login(
            @RequestBody LoginRequest request,
            HttpServletRequest httpRequest) {
        
        log.info("收到登入請求: username={}, clientId={}", request.getUsername(), request.getClientId());
        
        String ipAddress = getClientIpAddress(httpRequest);
        String userAgent = httpRequest.getHeader("User-Agent");
        
        Map<String, Object> result = sessionDemoService.performLogin(
            request.getUsername(),
            request.getClientId(),
            ipAddress,
            userAgent
        );
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * Session 查詢示範
     * 
     * @param jwtId JWT ID
     * @return 查詢結果
     */
    @GetMapping("/query/{jwtId}")
    public ResponseEntity<Map<String, Object>> querySession(@PathVariable String jwtId) {
        log.info("收到 Session 查詢請求: jwtId={}", jwtId);
        
        Map<String, Object> result = sessionDemoService.querySession(jwtId);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 用戶 Session 查詢示範
     * 
     * @param userId 用戶 ID
     * @return 查詢結果
     */
    @GetMapping("/user/{userId}")
    public ResponseEntity<Map<String, Object>> queryUserSessions(@PathVariable String userId) {
        log.info("收到用戶 Session 查詢請求: userId={}", userId);
        
        Map<String, Object> result = sessionDemoService.queryUserSessions(userId);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 批量操作示範
     * 
     * @param request 批量請求
     * @return 操作結果
     */
    @PostMapping("/batch")
    public ResponseEntity<Map<String, Object>> batchOperations(@RequestBody BatchRequest request) {
        log.info("收到批量操作請求: jwtIds={}", request.getJwtIds());
        
        Map<String, Object> result = sessionDemoService.batchOperations(request.getJwtIds());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * Session 續期示範
     * 
     * @param jwtId JWT ID
     * @param request 續期請求
     * @return 續期結果
     */
    @PostMapping("/renew/{jwtId}")
    public ResponseEntity<Map<String, Object>> renewSession(
            @PathVariable String jwtId,
            @RequestBody RenewRequest request) {
        
        log.info("收到 Session 續期請求: jwtId={}, newTtl={}", jwtId, request.getNewTtl());
        
        Map<String, Object> result = sessionDemoService.renewSession(jwtId, request.getNewTtl());
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 用戶登出示範
     * 
     * @param jwtId JWT ID
     * @return 登出結果
     */
    @PostMapping("/logout/{jwtId}")
    public ResponseEntity<Map<String, Object>> logout(@PathVariable String jwtId) {
        log.info("收到登出請求: jwtId={}", jwtId);
        
        Map<String, Object> result = sessionDemoService.performLogout(jwtId);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 快速測試示範（創建多個 Session）
     * 
     * @return 測試結果
     */
    @PostMapping("/quick-test")
    public ResponseEntity<Map<String, Object>> quickTest() {
        log.info("收到快速測試請求");
        
        // 創建多個用戶的 Session
        String[] usernames = {"admin", "john.doe", "jane.smith"};
        String[] clientIds = {"web-app", "mobile-app", "desktop-app"};
        
        Map<String, Object> result = new java.util.HashMap<>();
        List<Map<String, Object>> sessions = new java.util.ArrayList<>();
        
        for (int i = 0; i < usernames.length; i++) {
            Map<String, Object> sessionResult = sessionDemoService.performLogin(
                usernames[i],
                clientIds[i % clientIds.length],
                "127.0.0.1",
                "Demo-Test-Agent"
            );
            sessions.add(sessionResult);
        }
        
        result.put("success", true);
        result.put("message", "快速測試完成");
        result.put("createdSessions", sessions.size());
        result.put("sessions", sessions);
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 獲取客戶端 IP 地址
     * 
     * @param request HTTP 請求
     * @return IP 地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
    
    // 請求 DTO 類別
    
    public static class LoginRequest {
        private String username;
        private String clientId = "demo-app";
        
        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }
        public String getClientId() { return clientId; }
        public void setClientId(String clientId) { this.clientId = clientId; }
    }
    
    public static class BatchRequest {
        private List<String> jwtIds;
        
        // Getters and Setters
        public List<String> getJwtIds() { return jwtIds; }
        public void setJwtIds(List<String> jwtIds) { this.jwtIds = jwtIds; }
    }
    
    public static class RenewRequest {
        private long newTtl = 1800; // 預設 30 分鐘
        
        // Getters and Setters
        public long getNewTtl() { return newTtl; }
        public void setNewTtl(long newTtl) { this.newTtl = newTtl; }
    }
}
