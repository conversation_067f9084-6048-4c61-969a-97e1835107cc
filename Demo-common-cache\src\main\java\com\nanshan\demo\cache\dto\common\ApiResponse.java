package com.nanshan.demo.cache.dto.common;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 統一 API 回應格式
 * 
 * @param <T> 回應數據類型
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "統一 API 回應格式")
public class ApiResponse<T> {
    
    /**
     * 操作是否成功
     */
    @Schema(description = "操作是否成功", example = "true")
    private boolean success;
    
    /**
     * 回應訊息
     */
    @Schema(description = "回應訊息", example = "操作成功")
    private String message;
    
    /**
     * 回應數據
     */
    @Schema(description = "回應數據")
    private T data;
    
    /**
     * 錯誤代碼（當 success = false 時）
     */
    @Schema(description = "錯誤代碼", example = "CACHE_001")
    private String errorCode;
    
    /**
     * 回應時間戳
     */
    @Schema(description = "回應時間戳", example = "2025-07-22 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Builder.Default
    private LocalDateTime timestamp = LocalDateTime.now();
    
    /**
     * 建立成功回應
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .message("操作成功")
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 建立成功回應（自定義訊息）
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .message(message)
                .data(data)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 建立失敗回應
     */
    public static <T> ApiResponse<T> error(String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .timestamp(LocalDateTime.now())
                .build();
    }
    
    /**
     * 建立失敗回應（含錯誤代碼）
     */
    public static <T> ApiResponse<T> error(String errorCode, String message) {
        return ApiResponse.<T>builder()
                .success(false)
                .message(message)
                .errorCode(errorCode)
                .timestamp(LocalDateTime.now())
                .build();
    }
}
