package com.nanshan.demo.cache.dto.session;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Session 管理示範回應
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Session 管理示範回應")
public class SessionDemoResponse {
    
    /**
     * 示範描述
     */
    @Schema(description = "示範描述", example = "完整的 Session 管理示範，包含建立、查詢、續期、刪除等操作")
    private String description;
    
    /**
     * 執行步驟詳情
     */
    @Schema(description = "執行步驟詳情")
    private SessionDemoSteps steps;
    
    /**
     * 統計資訊
     */
    @Schema(description = "Session 統計資訊")
    private SessionStatsInfo stats;
    
    /**
     * Session 示範執行步驟
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "Session 示範執行步驟")
    public static class SessionDemoSteps {
        
        @Schema(description = "步驟 1: 建立 Session")
        private StepResult step1Create;
        
        @Schema(description = "步驟 2: 查詢 Session")
        private StepResult step2Get;
        
        @Schema(description = "步驟 3: 續期 Session")
        private StepResult step3Renew;
        
        @Schema(description = "步驟 4: 再次查詢 Session (驗證續期)")
        private StepResult step4GetAfterRenew;
        
        @Schema(description = "步驟 5: 獲取統計資訊")
        private StepResult step5Stats;
        
        @Schema(description = "步驟 6: 刪除 Session")
        private StepResult step6Delete;
        
        @Schema(description = "步驟 7: 驗證刪除 (再次查詢)")
        private StepResult step7GetAfterDelete;
    }
    
    /**
     * 單個步驟執行結果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "單個步驟執行結果")
    public static class StepResult {
        
        @Schema(description = "步驟是否成功", example = "true")
        private boolean success;
        
        @Schema(description = "步驟執行訊息", example = "Session 建立成功")
        private String message;
        
        @Schema(description = "步驟執行數據")
        private Map<String, Object> data;
        
        @Schema(description = "執行時間（毫秒）", example = "15")
        private Long executionTime;
    }
}
