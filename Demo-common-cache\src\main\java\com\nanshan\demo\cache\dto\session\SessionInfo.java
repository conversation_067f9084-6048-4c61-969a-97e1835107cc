package com.nanshan.demo.cache.dto.session;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Session 資訊
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Session 資訊")
public class SessionInfo {
    
    /**
     * JWT ID
     */
    @Schema(description = "JWT 唯一標識", example = "2821c1fd-70e9-4b08-b1f3-22e48842d849")
    private String jwtId;
    
    /**
     * 用戶 ID
     */
    @Schema(description = "用戶 ID", example = "demo-user-1753165388255")
    private String userId;
    
    /**
     * 用戶名
     */
    @Schema(description = "用戶名", example = "demo-user")
    private String username;
    
    /**
     * 客戶端 ID
     */
    @Schema(description = "客戶端 ID", example = "demo-client")
    private String clientId;
    
    /**
     * Session 建立時間
     */
    @Schema(description = "Session 建立時間", example = "2025-07-22 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * Session 過期時間
     */
    @Schema(description = "Session 過期時間", example = "2025-07-22 16:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;
    
    /**
     * 剩餘生存時間（秒）
     */
    @Schema(description = "剩餘生存時間（秒）", example = "3600")
    private Long ttl;
    
    /**
     * Session 狀態
     */
    @Schema(description = "Session 狀態", example = "ACTIVE", allowableValues = {"ACTIVE", "EXPIRED", "INVALID"})
    private String status;
    
    /**
     * 最後訪問時間
     */
    @Schema(description = "最後訪問時間", example = "2025-07-22 15:35:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastAccessTime;
    
    /**
     * Session 屬性
     */
    @Schema(description = "Session 屬性")
    private Map<String, Object> attributes;
    
    /**
     * 用戶角色
     */
    @Schema(description = "用戶角色", example = "[\"USER\", \"DEMO\"]")
    private java.util.List<String> roles;
    
    /**
     * 用戶權限
     */
    @Schema(description = "用戶權限", example = "[\"READ\", \"WRITE\"]")
    private java.util.List<String> permissions;
}
