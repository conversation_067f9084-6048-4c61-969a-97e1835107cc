package com.nanshan.demo.cache.dto.session;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Session 統計資訊
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "Session 統計資訊")
public class SessionStatsInfo {
    
    /**
     * 總 Session 數量
     */
    @Schema(description = "總 Session 數量", example = "150")
    private Long totalSessions;
    
    /**
     * 活躍 Session 數量
     */
    @Schema(description = "活躍 Session 數量", example = "120")
    private Long activeSessions;
    
    /**
     * 過期 Session 數量
     */
    @Schema(description = "過期 Session 數量", example = "30")
    private Long expiredSessions;
    
    /**
     * 今日建立的 Session 數量
     */
    @Schema(description = "今日建立的 Session 數量", example = "45")
    private Long todayCreated;
    
    /**
     * 今日過期的 Session 數量
     */
    @Schema(description = "今日過期的 Session 數量", example = "12")
    private Long todayExpired;
    
    /**
     * 平均 Session 生存時間（秒）
     */
    @Schema(description = "平均 Session 生存時間（秒）", example = "3600")
    private Double averageTtl;
    
    /**
     * 最大 Session 生存時間（秒）
     */
    @Schema(description = "最大 Session 生存時間（秒）", example = "7200")
    private Long maxTtl;
    
    /**
     * 最小 Session 生存時間（秒）
     */
    @Schema(description = "最小 Session 生存時間（秒）", example = "300")
    private Long minTtl;
    
    /**
     * Session 使用的記憶體大小（位元組）
     */
    @Schema(description = "Session 使用的記憶體大小（位元組）", example = "1048576")
    private Long memoryUsage;
    
    /**
     * 清理操作統計
     */
    @Schema(description = "清理操作統計")
    private CleanupStats cleanupStats;
    
    /**
     * 清理操作統計
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "清理操作統計")
    public static class CleanupStats {
        
        @Schema(description = "總清理次數", example = "25")
        private Long totalCleanups;
        
        @Schema(description = "清理的 Session 總數", example = "150")
        private Long cleanedSessions;
        
        @Schema(description = "最後清理時間", example = "2025-07-22 15:00:00")
        private String lastCleanupTime;
        
        @Schema(description = "平均清理時間（毫秒）", example = "50")
        private Double averageCleanupTime;
    }
}
