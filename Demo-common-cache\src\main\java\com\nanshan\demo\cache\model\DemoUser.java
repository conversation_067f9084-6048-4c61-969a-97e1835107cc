package com.nanshan.demo.cache.model;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 示範用戶資料模型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DemoUser implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 使用者 ID
     */
    private String userId;
    
    /**
     * 客戶端 ID
     */
    private String clientId;
    
    /**
     * 使用者名稱
     */
    private String username;

    /**
     * 顯示名稱
     */
    private String name;
    
    /**
     * 電子郵件
     */
    private String email;
    
    /**
     * 角色清單
     */
    private List<String> roles;
    
    /**
     * 權限清單
     */
    private List<String> permissions;
    
    /**
     * 部門
     */
    private String department;
    
    /**
     * 建立時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 最後登入時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime;
    
    /**
     * 是否啟用
     */
    private boolean enabled;
    
    /**
     * 便利方法：獲取 ID（兼容性）
     */
    public String getId() {
        return this.userId;
    }
    
    /**
     * 便利方法：設置 ID（兼容性）
     */
    public void setId(String id) {
        this.userId = id;
    }
}
