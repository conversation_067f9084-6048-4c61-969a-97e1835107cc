package com.nanshan.demo.cache.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 產品資料模型 (用於快取示範)
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Product implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 產品 ID
     */
    private String productId;
    
    /**
     * 產品名稱
     */
    private String name;
    
    /**
     * 產品描述
     */
    private String description;
    
    /**
     * 產品價格
     */
    private BigDecimal price;
    
    /**
     * 產品分類
     */
    private String category;
    
    /**
     * 產品標籤
     */
    private List<String> tags;
    
    /**
     * 庫存數量
     */
    private Integer stock;
    
    /**
     * 是否啟用
     */
    private boolean enabled;
    
    /**
     * 建立時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 更新時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 供應商資訊
     */
    private String supplier;
    
    /**
     * 產品規格
     */
    private String specifications;
}
