package com.nanshan.demo.cache.service;

import com.nanshan.demo.cache.model.DemoUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 認證服務（Demo 用途）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class AuthService {
    
    // 模擬用戶資料庫
    private final Map<String, DemoUser> userDatabase = new HashMap<>();
    
    public AuthService() {
        initializeUsers();
    }
    
    /**
     * 初始化示範用戶
     */
    private void initializeUsers() {
        // 管理員用戶
        DemoUser admin = DemoUser.builder()
                .userId("admin001")
                .username("admin")
                .name("系統管理員")
                .email("<EMAIL>")
                .roles(Arrays.asList("ADMIN", "USER"))
                .permissions(Arrays.asList("READ", "WRITE", "DELETE", "ADMIN"))
                .department("IT")
                .createdAt(LocalDateTime.now().minusDays(30))
                .lastLoginTime(LocalDateTime.now().minusHours(1))
                .enabled(true)
                .build();
        
        // 一般用戶
        DemoUser user1 = DemoUser.builder()
                .userId("user001")
                .username("john.doe")
                .name("約翰·多伊")
                .email("<EMAIL>")
                .roles(Arrays.asList("USER"))
                .permissions(Arrays.asList("READ", "WRITE"))
                .department("Sales")
                .createdAt(LocalDateTime.now().minusDays(15))
                .lastLoginTime(LocalDateTime.now().minusHours(2))
                .enabled(true)
                .build();
        
        // 測試用戶
        DemoUser user2 = DemoUser.builder()
                .userId("user002")
                .username("jane.smith")
                .name("珍·史密斯")
                .email("<EMAIL>")
                .roles(Arrays.asList("USER", "MANAGER"))
                .permissions(Arrays.asList("READ", "WRITE", "MANAGE"))
                .department("Marketing")
                .createdAt(LocalDateTime.now().minusDays(7))
                .lastLoginTime(LocalDateTime.now().minusMinutes(30))
                .enabled(true)
                .build();
        
        userDatabase.put("admin", admin);
        userDatabase.put("john.doe", user1);
        userDatabase.put("jane.smith", user2);
        
        log.info("初始化了 {} 個示範用戶", userDatabase.size());
    }
    
    /**
     * 用戶認證（簡化版，實際應用中應該驗證密碼）
     * 
     * @param username 用戶名
     * @param password 密碼（Demo 中忽略）
     * @return 用戶資訊
     */
    public DemoUser authenticate(String username, String password) {
        DemoUser user = userDatabase.get(username);
        if (user == null) {
            throw new RuntimeException("用戶不存在: " + username);
        }
        
        if (!user.isEnabled()) {
            throw new RuntimeException("用戶已被禁用: " + username);
        }
        
        // 更新最後登入時間
        user.setLastLoginTime(LocalDateTime.now());
        
        log.info("用戶認證成功: {}", username);
        return user;
    }
    
    /**
     * 根據用戶 ID 獲取用戶資訊
     * 
     * @param userId 用戶 ID
     * @return 用戶資訊
     */
    public DemoUser getUserById(String userId) {
        return userDatabase.values().stream()
                .filter(user -> user.getUserId().equals(userId))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 獲取所有用戶
     * 
     * @return 用戶列表
     */
    public List<DemoUser> getAllUsers() {
        return userDatabase.values().stream().toList();
    }
    
    /**
     * 檢查用戶是否有指定角色
     * 
     * @param user 用戶
     * @param role 角色
     * @return 是否有角色
     */
    public boolean hasRole(DemoUser user, String role) {
        return user.getRoles().contains(role);
    }
    
    /**
     * 檢查用戶是否有指定權限
     * 
     * @param user 用戶
     * @param permission 權限
     * @return 是否有權限
     */
    public boolean hasPermission(DemoUser user, String permission) {
        return user.getPermissions().contains(permission);
    }
}
