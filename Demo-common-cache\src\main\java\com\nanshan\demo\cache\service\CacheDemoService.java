package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.demo.cache.model.Product;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 快取示範服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CacheDemoService {

    private final CacheManager cacheManager;

    private static final String CACHE_TYPE_PRODUCT = "product";
    private static final String CACHE_TYPE_USER_PROFILE = "user_profile";

    /**
     * 快取基本操作示範
     *
     * @return 示範結果
     */
    public Map<String, Object> basicCacheDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 建立示範產品
            Product product = createDemoProduct("PROD-001");

            // 1. 儲存快取
            cacheManager.put(CACHE_TYPE_PRODUCT, product.getProductId(), product, 3600, TimeUnit.SECONDS);
            log.info("儲存產品快取: {}", product.getProductId());

            // 2. 查詢快取
            Optional<Product> cachedProduct = cacheManager.get(CACHE_TYPE_PRODUCT, product.getProductId(), Product.class);

            // 3. 檢查快取是否存在
            boolean exists = cacheManager.exists(CACHE_TYPE_PRODUCT, product.getProductId());

            // 4. 暫時移除 TTL 檢查（等待 API 更新）
            // long ttl = cacheManager.getRemainingTimeToLive(CACHE_TYPE_PRODUCT, product.getProductId(), TimeUnit.SECONDS);
            // 5. 續期快取
            boolean renewed = cacheManager.renew(CACHE_TYPE_PRODUCT, product.getProductId(), 7200, TimeUnit.SECONDS);

            // 6. 暫時移除 TTL 檢查
            // long newTtl = cacheManager.getRemainingTimeToLive(CACHE_TYPE_PRODUCT, product.getProductId(), TimeUnit.SECONDS);
            // 返回結果
            result.put("success", true);
            result.put("message", "快取基本操作示範完成");
            result.put("originalProduct", product);
            result.put("cachedProduct", cachedProduct.orElse(null));
            result.put("exists", exists);
            result.put("originalTtl", -1); // 暫時設為 -1
            result.put("renewed", renewed);
            result.put("newTtl", -1); // 暫時設為 -1

            log.info("快取基本操作示範完成");

        } catch (Exception e) {
            log.error("快取基本操作示範失敗", e);
            result.put("success", false);
            result.put("message", "快取基本操作示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 快取批量操作示範
     *
     * @return 示範結果
     */
    public Map<String, Object> batchCacheDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 建立多個示範產品
            Map<String, Product> products = new HashMap<>();
            for (int i = 1; i <= 5; i++) {
                Product product = createDemoProduct("BATCH-PROD-" + String.format("%03d", i));
                products.put(product.getProductId(), product);
            }

            // 1. 批量儲存快取
            cacheManager.multiPut(CACHE_TYPE_PRODUCT, products, 3600, TimeUnit.SECONDS);
            log.info("批量儲存產品快取: {} 個產品", products.size());

            // 2. 批量查詢快取
            Set<String> productIds = products.keySet();
            Map<String, Product> cachedProducts = cacheManager.multiGet(CACHE_TYPE_PRODUCT, productIds, Product.class);

            // 3. 暫時移除獲取所有 Key 的功能
            Set<String> allKeys = Set.of("暫時無法獲取");

            // 4. 批量刪除部分快取（暫時使用單個刪除）
            Set<String> keysToDelete = productIds.stream().limit(2).collect(Collectors.toSet());
            long deletedCount = 0;
            for (String key : keysToDelete) {
                if (cacheManager.remove(CACHE_TYPE_PRODUCT, key)) {
                    deletedCount++;
                }
            }

            // 5. 暫時移除獲取所有 Key 的功能
            Set<String> remainingKeys = Set.of("暫時無法獲取");

            // 返回結果
            result.put("success", true);
            result.put("message", "快取批量操作示範完成");
            result.put("originalProducts", products);
            result.put("cachedProducts", cachedProducts);
            result.put("allKeysBeforeDelete", allKeys);
            result.put("deletedCount", deletedCount);
            result.put("remainingKeys", remainingKeys);

            log.info("快取批量操作示範完成");

        } catch (Exception e) {
            log.error("快取批量操作示範失敗", e);
            result.put("success", false);
            result.put("message", "快取批量操作示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 快取計算操作示範 (getOrCompute)
     *
     * @return 示範結果
     */
    public Map<String, Object> computeCacheDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            String productId = "COMPUTE-PROD-001";

            // 1. 第一次調用 (快取未命中，執行計算)
            long startTime1 = System.currentTimeMillis();
            Optional<Product> cachedProduct1 = cacheManager.get(CACHE_TYPE_PRODUCT, productId, Product.class);
            Product product1;
            if (cachedProduct1.isPresent()) {
                product1 = cachedProduct1.get();
                log.info("快取命中: {}", productId);
            } else {
                log.info("快取未命中，執行產品資料計算 (模擬資料庫查詢): {}", productId);
                // 模擬耗時的資料庫查詢
                try {
                    Thread.sleep(1000); // 模擬 1 秒查詢時間
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
                product1 = createDemoProduct(productId);
                // 儲存到快取
                cacheManager.put(CACHE_TYPE_PRODUCT, productId, product1, 3600, TimeUnit.SECONDS);
            }
            long duration1 = System.currentTimeMillis() - startTime1;

            // 2. 第二次調用 (快取命中，直接返回)
            long startTime2 = System.currentTimeMillis();
            Optional<Product> cachedProduct2 = cacheManager.get(CACHE_TYPE_PRODUCT, productId, Product.class);
            Product product2 = cachedProduct2.orElse(null);
            long duration2 = System.currentTimeMillis() - startTime2;

            // 3. 檢查快取是否存在
            boolean exists = cacheManager.exists(CACHE_TYPE_PRODUCT, productId);

            // 4. 檢查快取是否存在（暫時移除 TTL 檢查）
            // long ttl = cacheManager.getRemainingTimeToLive(CACHE_TYPE_PRODUCT, productId, TimeUnit.SECONDS);
            // 返回結果
            result.put("success", true);
            result.put("message", "快取計算操作示範完成");
            result.put("firstCall", Map.of(
                    "product", product1,
                    "duration", duration1,
                    "description", "快取未命中，執行計算"
            ));
            result.put("secondCall", Map.of(
                    "product", product2,
                    "duration", duration2,
                    "description", "快取命中，直接返回"
            ));
            result.put("exists", exists);
            result.put("ttl", -1); // 暫時設為 -1

            log.info("快取計算操作示範完成，第一次耗時: {}ms，第二次耗時: {}ms", duration1, duration2);

        } catch (Exception e) {
            log.error("快取計算操作示範失敗", e);
            result.put("success", false);
            result.put("message", "快取計算操作示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 快取統計資訊示範
     *
     * @return 示範結果
     */
    public Map<String, Object> cacheStatsDemo() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 建立一些快取資料以產生統計資訊
            for (int i = 1; i <= 3; i++) {
                Product product = createDemoProduct("STATS-PROD-" + String.format("%03d", i));
                cacheManager.put(CACHE_TYPE_PRODUCT, product.getProductId(), product);
            }

            // 執行一些查詢操作以產生命中/未命中統計
            cacheManager.get(CACHE_TYPE_PRODUCT, "STATS-PROD-001", Product.class); // 命中
            cacheManager.get(CACHE_TYPE_PRODUCT, "STATS-PROD-002", Product.class); // 命中
            cacheManager.get(CACHE_TYPE_PRODUCT, "NON-EXISTENT", Product.class);   // 未命中

            // 暫時簡化統計功能
            // CacheManager.CacheStats overallStats = cacheManager.getStats();
            // 返回結果
            result.put("success", true);
            result.put("message", "快取統計資訊示範完成");
            result.put("overallStats", Map.of(
                    "hitCount", 0L,
                    "missCount", 0L,
                    "hitRate", 0.0,
                    "totalRequests", 0L
            ));

            // 清理過期快取並獲取清理統計
            long cleanedCount = cacheManager.cleanupExpired();
            result.put("cleanupStats", Map.of(
                    "cleanedCount", cleanedCount
            ));

            log.info("快取統計資訊示範完成");

        } catch (Exception e) {
            log.error("快取統計資訊示範失敗", e);
            result.put("success", false);
            result.put("message", "快取統計資訊示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 建立示範產品
     *
     * @param productId 產品 ID
     * @return 示範產品
     */
    private Product createDemoProduct(String productId) {
        return Product.builder()
                .productId(productId)
                .name("示範產品 " + productId)
                .description("這是一個用於快取示範的產品")
                .price(new BigDecimal("99.99"))
                .category("電子產品")
                .tags(Arrays.asList("示範", "快取", "測試"))
                .stock(100)
                .enabled(true)
                .createdAt(LocalDateTime.now().minusDays(7))
                .updatedAt(LocalDateTime.now())
                .supplier("示範供應商")
                .specifications("示範規格")
                .build();
    }
}
