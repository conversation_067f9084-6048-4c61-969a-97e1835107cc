package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.demo.cache.model.DemoUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SessionManager 和 CacheManager 協同工作示範服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CollaborationDemoService {

    private final SessionManager sessionManager;
    private final CacheManager cacheManager;
    private final AuthService authService;
    private final JwtService jwtService;

    /**
     * 完整的用戶登入流程 - SessionManager + CacheManager 協同
     *
     * @param username 用戶名
     * @param clientId 客戶端 ID
     * @param ipAddress IP 地址
     * @param userAgent User Agent
     * @return 登入結果
     */
    public Map<String, Object> performCompleteLogin(String username, String clientId, String ipAddress, String userAgent) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始完整登入流程: username={}, clientId={}", username, clientId);

            // 1. 用戶認證
            DemoUser user = authService.authenticate(username, "demo-password");

            // 2. 快取用戶資料（CacheManager）
            cacheManager.put("user", user.getUserId(), user, 3600, TimeUnit.SECONDS);
            log.info("用戶資料已快取: userId={}", user.getUserId());

            // 3. 快取用戶權限（CacheManager）
            List<String> permissions = user.getPermissions();
            cacheManager.put("user_permissions", user.getUserId(), permissions, 1800, TimeUnit.SECONDS);
            log.info("用戶權限已快取: userId={}, permissions={}", user.getUserId(), permissions);

            // 4. 生成 JWT Token
            String jwtId = UUID.randomUUID().toString();
            String jwtToken = jwtService.generateToken(user, jwtId);

            // 5. 創建 Session（SessionManager）
            SessionObject session = com.nanshan.common.cache.model.SessionObjectBuilder.createFullSession(
                    user.getUserId(),
                    clientId,
                    jwtId,
                    user.getRoles(),
                    user.getPermissions(),
                    ipAddress,
                    "Demo Device",
                    userAgent,
                    1800 // TTL 秒數
            );

            // 6. 添加 Session 自定義屬性
            session.getAttributes().put("name", user.getName());
            session.getAttributes().put("email", user.getEmail());
            session.getAttributes().put("department", user.getDepartment());
            session.getAttributes().put("loginMethod", "collaboration-demo");

            // 7. 儲存 Session
            sessionManager.saveSession(jwtId, session, 1800);
            log.info("Session 已創建: jwtId={}", jwtId);

            // 8. 快取登入統計
            updateLoginStatistics(user.getUserId());

            // 9. 返回結果
            result.put("success", true);
            result.put("message", "完整登入流程成功");
            result.put("jwtToken", jwtToken);
            result.put("jwtId", jwtId);
            result.put("user", user);
            result.put("session", session);
            result.put("cacheInfo", Map.of(
                    "userCached", true,
                    "permissionsCached", true,
                    "statisticsUpdated", true
            ));

            log.info("完整登入流程成功: userId={}, jwtId={}", user.getUserId(), jwtId);

        } catch (Exception e) {
            log.error("完整登入流程失敗: username={}", username, e);
            result.put("success", false);
            result.put("message", "登入失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * API 請求處理流程 - SessionManager + CacheManager 協同
     *
     * @param jwtToken JWT Token
     * @return 處理結果
     */
    public Map<String, Object> processApiRequest(String jwtToken) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始 API 請求處理流程");

            // 1. 解析 JWT Token
            String jwtId = jwtService.extractJwtId(jwtToken);
            String userId = jwtService.extractUserId(jwtToken);

            // 2. 驗證 Session 狀態（SessionManager）
            Optional<SessionObject> sessionOpt = sessionManager.loadSession(jwtId);
            if (!sessionOpt.isPresent()) {
                result.put("success", false);
                result.put("message", "Session 已過期或不存在");
                return result;
            }

            SessionObject session = sessionOpt.get();
            log.info("Session 驗證成功: jwtId={}, userId={}", jwtId, userId);

            // 3. 獲取用戶資料（CacheManager）
            Optional<DemoUser> userOpt = cacheManager.get("user", userId, DemoUser.class);
            DemoUser user;
            if (userOpt.isPresent()) {
                user = userOpt.get();
            } else {
                log.info("快取未命中，從資料庫載入用戶: userId={}", userId);
                user = authService.getUserById(userId);
                cacheManager.put("user", userId, user, 3600, TimeUnit.SECONDS);
            }

            // 4. 獲取用戶權限（CacheManager）
            @SuppressWarnings("unchecked")
            Optional<List> permissionsOpt = cacheManager.get("user_permissions", userId, List.class);
            List<String> permissions;
            if (permissionsOpt.isPresent()) {
                permissions = (List<String>) permissionsOpt.get();
            } else {
                log.info("快取未命中，重新計算權限: userId={}", userId);
                permissions = user.getPermissions();
                cacheManager.put("user_permissions", userId, permissions, 1800, TimeUnit.SECONDS);
            }

            // 5. 獲取用戶偏好設定（CacheManager）
            Optional<Map> preferencesOpt = cacheManager.get("user_preferences", userId, Map.class);
            Map<String, Object> preferences;
            if (preferencesOpt.isPresent()) {
                preferences = (Map<String, Object>) preferencesOpt.get();
            } else {
                log.info("載入用戶偏好設定: userId={}", userId);
                preferences = createDefaultPreferences();
                cacheManager.put("user_preferences", userId, preferences, 7200, TimeUnit.SECONDS);
            }

            // 6. 更新 Session 最後活動時間
            session.updateLastActiveTime();
            sessionManager.saveSession(jwtId, session, 1800);

            // 7. 返回結果
            result.put("success", true);
            result.put("message", "API 請求處理成功");
            result.put("user", user);
            result.put("permissions", permissions);
            result.put("preferences", preferences);
            result.put("session", session);
            result.put("cacheHits", Map.of(
                    "userCache", cacheManager.get("user", userId, DemoUser.class).isPresent(),
                    "permissionsCache", cacheManager.get("user_permissions", userId, List.class).isPresent(),
                    "preferencesCache", cacheManager.get("user_preferences", userId, Map.class).isPresent()
            ));

            log.info("API 請求處理成功: userId={}", userId);

        } catch (Exception e) {
            log.error("API 請求處理失敗", e);
            result.put("success", false);
            result.put("message", "API 請求處理失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 用戶資料更新流程 - SessionManager + CacheManager 協同
     *
     * @param userId 用戶 ID
     * @param updatedUser 更新的用戶資料
     * @return 更新結果
     */
    public Map<String, Object> updateUserData(String userId, DemoUser updatedUser) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始用戶資料更新流程: userId={}", userId);

            // 1. 更新快取中的用戶資料（CacheManager）
            cacheManager.put("user", userId, updatedUser, 3600, TimeUnit.SECONDS);
            log.info("用戶資料快取已更新: userId={}", userId);

            // 2. 如果角色變更，清除權限快取
            if (rolesChanged(userId, updatedUser)) {
                cacheManager.remove("user_permissions", userId);
                log.info("用戶角色變更，權限快取已清除: userId={}", userId);
            }

            // 3. 如果是敏感變更，強制重新登入（SessionManager）
            if (isSensitiveChange(updatedUser)) {
                List<SessionObject> sessions = sessionManager.findSessionsByUserId(userId);
                for (SessionObject session : sessions) {
                    sessionManager.deleteSession(session.getJwtId());
                }
                log.info("敏感變更檢測，已強制用戶重新登入: userId={}, sessionsDeleted={}",
                        userId, sessions.size());

                result.put("forceRelogin", true);
                result.put("deletedSessions", sessions.size());
            }

            // 4. 更新相關快取
            updateRelatedCaches(userId, updatedUser);

            // 5. 返回結果
            result.put("success", true);
            result.put("message", "用戶資料更新成功");
            result.put("userId", userId);
            result.put("updatedUser", updatedUser);
            result.put("cacheUpdated", true);

            log.info("用戶資料更新成功: userId={}", userId);

        } catch (Exception e) {
            log.error("用戶資料更新失敗: userId={}", userId, e);
            result.put("success", false);
            result.put("message", "用戶資料更新失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 批量用戶操作示範
     *
     * @param userIds 用戶 ID 列表
     * @return 操作結果
     */
    public Map<String, Object> batchUserOperations(List<String> userIds) {
        Map<String, Object> result = new HashMap<>();

        try {
            log.info("開始批量用戶操作: userIds={}", userIds);

            // 1. 批量獲取用戶資料（CacheManager）
            Map<String, DemoUser> users = cacheManager.multiGet("user", userIds, DemoUser.class);

            // 2. 批量獲取 Session 資訊（SessionManager）
            Map<String, List<SessionObject>> userSessions = new HashMap<>();
            for (String userId : userIds) {
                List<SessionObject> sessions = sessionManager.findSessionsByUserId(userId);
                userSessions.put(userId, sessions);
            }

            // 3. 統計資訊
            long totalSessions = userSessions.values().stream()
                    .mapToLong(List::size)
                    .sum();

            long activeSessions = userSessions.values().stream()
                    .flatMap(List::stream)
                    .mapToLong(session -> sessionManager.getSessionRemainingTimeToLive(session.getJwtId(), TimeUnit.SECONDS))
                    .filter(ttl -> ttl > 0)
                    .count();

            // 4. 返回結果
            result.put("success", true);
            result.put("message", "批量用戶操作成功");
            result.put("requestedUsers", userIds.size());
            result.put("foundUsers", users.size());
            result.put("users", users);
            result.put("userSessions", userSessions);
            result.put("totalSessions", totalSessions);
            result.put("activeSessions", activeSessions);

            log.info("批量用戶操作成功: requested={}, found={}, totalSessions={}, activeSessions={}",
                    userIds.size(), users.size(), totalSessions, activeSessions);

        } catch (Exception e) {
            log.error("批量用戶操作失敗: userIds={}", userIds, e);
            result.put("success", false);
            result.put("message", "批量用戶操作失敗: " + e.getMessage());
        }

        return result;
    }

    // 輔助方法
    private void updateLoginStatistics(String userId) {
        try {
            // 獲取當前統計
            Optional<Map> statsOpt = cacheManager.get("login_stats", userId, Map.class);
            Map<String, Object> stats;
            if (statsOpt.isPresent()) {
                stats = (Map<String, Object>) statsOpt.get();
            } else {
                stats = new HashMap<String, Object>();
                cacheManager.put("login_stats", userId, stats, 86400, TimeUnit.SECONDS);
            }

            // 更新統計
            stats.put("lastLoginTime", LocalDateTime.now());
            stats.put("loginCount", ((Integer) stats.getOrDefault("loginCount", 0)) + 1);

            // 儲存更新的統計
            cacheManager.put("login_stats", userId, stats, 86400, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.warn("更新登入統計失敗: userId={}", userId, e);
        }
    }

    private boolean rolesChanged(String userId, DemoUser updatedUser) {
        try {
            Optional<DemoUser> cachedUserOpt = cacheManager.get("user", userId, DemoUser.class);
            if (cachedUserOpt.isPresent()) {
                DemoUser cachedUser = cachedUserOpt.get();
                return !Objects.equals(cachedUser.getRoles(), updatedUser.getRoles());
            }
        } catch (Exception e) {
            log.warn("檢查角色變更失敗: userId={}", userId, e);
        }
        return false;
    }

    private boolean isSensitiveChange(DemoUser updatedUser) {
        // 簡化的敏感變更檢測邏輯
        return updatedUser.getRoles().contains("ADMIN")
                || updatedUser.getPermissions().contains("ADMIN");
    }

    private void updateRelatedCaches(String userId, DemoUser updatedUser) {
        try {
            // 更新部門相關快取
            if (updatedUser.getDepartment() != null) {
                String deptKey = "dept_users:" + updatedUser.getDepartment();
                // 這裡可以更新部門用戶列表等相關快取
            }
        } catch (Exception e) {
            log.warn("更新相關快取失敗: userId={}", userId, e);
        }
    }

    private Map<String, Object> createDefaultPreferences() {
        Map<String, Object> preferences = new HashMap<>();
        preferences.put("language", "zh-TW");
        preferences.put("timezone", "Asia/Taipei");
        preferences.put("theme", "light");
        preferences.put("pageSize", 20);
        return preferences;
    }
}
