package com.nanshan.demo.cache.service;

import com.nanshan.demo.cache.config.DemoProperties;
import com.nanshan.demo.cache.model.DemoUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.UUID;

/**
 * JWT Token 服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class JwtService {

    private final DemoProperties demoProperties;

    /**
     * 生成 JWT Token（使用隨機 JWT ID）
     *
     * @param user 用戶資訊
     * @return JWT Token
     */
    public String generateToken(DemoUser user) {
        return generateToken(user, UUID.randomUUID().toString());
    }

    /**
     * 生成 JWT Token
     *
     * @param user 用戶資訊
     * @param jwtId JWT ID
     * @return JWT Token
     */
    public String generateToken(DemoUser user, String jwtId) {
        Date now = new Date();
        Date expiration = new Date(now.getTime() + demoProperties.getJwt().getExpiration() * 1000);

        SecretKey key = Keys.hmacShaKeyFor(demoProperties.getJwt().getSecret().getBytes());

        return Jwts.builder()
                .setSubject(user.getUserId())
                .setId(jwtId)
                .setIssuer(demoProperties.getJwt().getIssuer())
                .setIssuedAt(now)
                .setExpiration(expiration)
                .claim("userId", user.getUserId())
                .claim("clientId", user.getClientId())
                .claim("username", user.getUsername())
                .claim("name", user.getName())
                .claim("roles", user.getRoles())
                .claim("permissions", user.getPermissions())
                .signWith(key)
                .compact();
    }

    /**
     * 解析 JWT Token
     *
     * @param token JWT Token
     * @return Claims
     */
    public Claims parseToken(String token) {
        SecretKey key = Keys.hmacShaKeyFor(demoProperties.getJwt().getSecret().getBytes());

        return Jwts.parser()
                .setSigningKey(key)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 從 Token 中提取 JWT ID
     *
     * @param token JWT Token
     * @return JWT ID
     */
    public String extractJwtId(String token) {
        Claims claims = parseToken(token);
        return claims.getId();
    }

    /**
     * 從 Token 中提取用戶 ID
     *
     * @param token JWT Token
     * @return 用戶 ID
     */
    public String extractUserId(String token) {
        Claims claims = parseToken(token);
        return claims.getSubject();
    }

    /**
     * 從 Token 中提取客戶端 ID
     *
     * @param token JWT Token
     * @return 客戶端 ID
     */
    public String extractClientId(String token) {
        Claims claims = parseToken(token);
        return claims.get("clientId", String.class);
    }

    /**
     * 驗證 Token 是否有效
     *
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            parseToken(token);
            return true;
        } catch (Exception e) {
            log.debug("Invalid JWT token: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 檢查 Token 是否過期
     *
     * @param token JWT Token
     * @return 是否過期
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = parseToken(token);
            return claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return true;
        }
    }
}
