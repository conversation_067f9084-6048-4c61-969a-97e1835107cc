package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.service.LockManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 分散式鎖示範服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LockDemoService {

    private final LockManager lockManager;

    private final AtomicInteger counter = new AtomicInteger(0);

    /**
     * 可重入鎖示範
     *
     * @return 示範結果
     */
    public Map<String, Object> reentrantLockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:reentrant:order-process";

        try {
            log.info("開始可重入鎖示範");

            // 嘗試獲取鎖
            boolean lockAcquired = lockManager.tryLock(lockResource, 5, 30, TimeUnit.SECONDS);

            if (lockAcquired) {
                try {
                    log.info("成功獲取可重入鎖: {}", lockResource);

                    // 模擬業務處理
                    processOrder();

                    // 測試可重入性 - 在同一線程中再次獲取鎖
                    boolean reentrantAcquired = lockManager.tryLock(lockResource, 1, 10, TimeUnit.SECONDS);

                    if (reentrantAcquired) {
                        try {
                            log.info("成功重入鎖: {}", lockResource);

                            // 模擬嵌套業務處理
                            updateOrderStatus();

                            result.put("reentrantSuccess", true);
                        } finally {
                            // 釋放重入的鎖
                            lockManager.unlock(lockResource);
                            log.info("釋放重入鎖: {}", lockResource);
                        }
                    } else {
                        result.put("reentrantSuccess", false);
                        log.warn("重入鎖獲取失敗: {}", lockResource);
                    }

                } finally {
                    // 釋放主鎖
                    lockManager.unlock(lockResource);
                    log.info("釋放主鎖: {}", lockResource);
                }

                result.put("success", true);
                result.put("message", "可重入鎖示範完成");
                result.put("lockAcquired", true);

            } else {
                result.put("success", false);
                result.put("message", "無法獲取可重入鎖");
                result.put("lockAcquired", false);

                log.warn("無法獲取可重入鎖: {}", lockResource);
            }

        } catch (Exception e) {
            log.error("可重入鎖示範失敗", e);
            result.put("success", false);
            result.put("message", "可重入鎖示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 公平鎖示範
     *
     * @return 示範結果
     */
    public Map<String, Object> fairLockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:fair:queue-process";

        try {
            log.info("開始公平鎖示範");

            // 嘗試獲取公平鎖
            boolean lockAcquired = lockManager.tryFairLock(lockResource, 5, 20, TimeUnit.SECONDS);

            if (lockAcquired) {
                try {
                    log.info("成功獲取公平鎖: {}", lockResource);

                    // 模擬按順序處理隊列
                    processQueue();

                    result.put("success", true);
                    result.put("message", "公平鎖示範完成");
                    result.put("lockAcquired", true);

                } finally {
                    // 釋放公平鎖
                    lockManager.unlock(lockResource);
                    log.info("釋放公平鎖: {}", lockResource);
                }

            } else {
                result.put("success", false);
                result.put("message", "無法獲取公平鎖");
                result.put("lockAcquired", false);

                log.warn("無法獲取公平鎖: {}", lockResource);
            }

        } catch (Exception e) {
            log.error("公平鎖示範失敗", e);
            result.put("success", false);
            result.put("message", "公平鎖示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 讀寫鎖示範
     *
     * @return 示範結果
     */
    public Map<String, Object> readWriteLockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:readwrite:data-access";

        try {
            log.info("開始讀寫鎖示範");

            // 1. 讀鎖示範
            boolean readLockAcquired = lockManager.tryReadLock(lockResource, 5, 15, TimeUnit.SECONDS);

            if (readLockAcquired) {
                try {
                    log.info("成功獲取讀鎖: {}", lockResource);

                    // 模擬讀取資料
                    String data = readData();
                    result.put("readData", data);

                } finally {
                    // 釋放讀鎖
                    lockManager.unlock(lockResource);
                    log.info("釋放讀鎖: {}", lockResource);
                }
            }

            // 2. 寫鎖示範
            boolean writeLockAcquired = lockManager.tryWriteLock(lockResource, 5, 15, TimeUnit.SECONDS);

            if (writeLockAcquired) {
                try {
                    log.info("成功獲取寫鎖: {}", lockResource);

                    // 模擬寫入資料
                    String newData = writeData();
                    result.put("writtenData", newData);

                } finally {
                    // 釋放寫鎖
                    lockManager.unlock(lockResource);
                    log.info("釋放寫鎖: {}", lockResource);
                }
            }

            result.put("success", true);
            result.put("message", "讀寫鎖示範完成");
            result.put("readLockAcquired", readLockAcquired);
            result.put("writeLockAcquired", writeLockAcquired);

        } catch (Exception e) {
            log.error("讀寫鎖示範失敗", e);
            result.put("success", false);
            result.put("message", "讀寫鎖示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 鎖保護執行示範
     *
     * @return 示範結果
     */
    public Map<String, Object> executeWithLockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:execute:critical-section";

        try {
            log.info("開始鎖保護執行示範");

            // 使用 lock/unlock 方法
            String executionResult;
            try {
                lockManager.lock(lockResource, 20, TimeUnit.SECONDS);
                log.info("在鎖保護下執行關鍵業務邏輯");

                // 模擬關鍵業務處理
                executionResult = performCriticalOperation();
            } finally {
                lockManager.unlock(lockResource);
            }

            result.put("success", true);
            result.put("message", "鎖保護執行示範完成");
            result.put("executionResult", executionResult);

            log.info("鎖保護執行示範完成，結果: {}", executionResult);

        } catch (Exception e) {
            log.error("鎖保護執行示範失敗", e);
            result.put("success", false);
            result.put("message", "鎖保護執行示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 併發鎖測試示範
     *
     * @return 示範結果
     */
    public Map<String, Object> concurrentLockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:concurrent:counter";

        try {
            log.info("開始併發鎖測試示範");

            // 重置計數器
            counter.set(0);

            // 啟動多個併發任務
            CompletableFuture<String>[] futures = new CompletableFuture[5];

            for (int i = 0; i < 5; i++) {
                final int taskId = i + 1;
                futures[i] = CompletableFuture.supplyAsync(() -> {
                    try {
                        lockManager.lock(lockResource, 30, TimeUnit.SECONDS);
                        log.info("任務 {} 獲取鎖，開始執行", taskId);

                        // 模擬業務處理時間
                        try {
                            Thread.sleep(500);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }

                        int currentValue = counter.incrementAndGet();
                        log.info("任務 {} 完成，計數器值: {}", taskId, currentValue);

                        return "任務 " + taskId + " 完成，計數器: " + currentValue;
                    } finally {
                        lockManager.unlock(lockResource);
                    }
                });
            }

            // 等待所有任務完成
            CompletableFuture.allOf(futures).join();

            // 收集結果
            String[] taskResults = new String[5];
            for (int i = 0; i < 5; i++) {
                taskResults[i] = futures[i].join();
            }

            result.put("success", true);
            result.put("message", "併發鎖測試示範完成");
            result.put("finalCounterValue", counter.get());
            result.put("taskResults", taskResults);

            log.info("併發鎖測試示範完成，最終計數器值: {}", counter.get());

        } catch (Exception e) {
            log.error("併發鎖測試示範失敗", e);
            result.put("success", false);
            result.put("message", "併發鎖測試示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 鎖狀態檢查示範
     *
     * @return 示範結果
     */
    public Map<String, Object> lockStatusDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:status:check";

        try {
            log.info("開始鎖狀態檢查示範");

            // 1. 檢查鎖是否被持有
            boolean isLocked = lockManager.isLocked(lockResource);
            log.info("鎖 {} 是否被持有: {}", lockResource, isLocked);

            // 2. 獲取鎖
            boolean lockAcquired = lockManager.tryLock(lockResource, 1, 10, TimeUnit.SECONDS);

            if (lockAcquired) {
                try {
                    // 3. 再次檢查鎖狀態
                    boolean isLockedAfterAcquire = lockManager.isLocked(lockResource);
                    boolean isHeldByCurrentThread = lockManager.isHeldByCurrentThread(lockResource);
                    long remainingLeaseTime = lockManager.getRemainingLeaseTime(lockResource, TimeUnit.SECONDS);

                    log.info("獲取鎖後狀態 - 是否被持有: {}, 是否被當前線程持有: {}, 剩餘租約時間: {}秒",
                            isLockedAfterAcquire, isHeldByCurrentThread, remainingLeaseTime);

                    result.put("success", true);
                    result.put("message", "鎖狀態檢查示範完成");
                    result.put("initialLocked", isLocked);
                    result.put("lockedAfterAcquire", isLockedAfterAcquire);
                    result.put("heldByCurrentThread", isHeldByCurrentThread);
                    result.put("remainingLeaseTime", remainingLeaseTime);

                } finally {
                    // 4. 釋放鎖
                    boolean unlocked = lockManager.unlock(lockResource);
                    log.info("鎖釋放結果: {}", unlocked);

                    // 5. 檢查釋放後的狀態
                    boolean isLockedAfterRelease = lockManager.isLocked(lockResource);
                    log.info("釋放鎖後狀態 - 是否被持有: {}", isLockedAfterRelease);

                    result.put("unlocked", unlocked);
                    result.put("lockedAfterRelease", isLockedAfterRelease);
                }
            } else {
                result.put("success", false);
                result.put("message", "無法獲取鎖進行狀態檢查");
                log.warn("無法獲取鎖: {}", lockResource);
            }

        } catch (Exception e) {
            log.error("鎖狀態檢查示範失敗", e);
            result.put("success", false);
            result.put("message", "鎖狀態檢查示範失敗: " + e.getMessage());
        }

        return result;
    }

    /**
     * 強制解鎖示範
     *
     * @return 示範結果
     */
    public Map<String, Object> forceUnlockDemo() {
        Map<String, Object> result = new HashMap<>();
        String lockResource = "demo:force:unlock";

        try {
            log.info("開始強制解鎖示範");

            // 1. 獲取鎖
            boolean lockAcquired = lockManager.tryLock(lockResource, 1, 30, TimeUnit.SECONDS);

            if (lockAcquired) {
                log.info("成功獲取鎖: {}", lockResource);

                // 2. 檢查鎖狀態
                boolean isLocked = lockManager.isLocked(lockResource);
                boolean isHeldByCurrentThread = lockManager.isHeldByCurrentThread(lockResource);

                log.info("鎖狀態 - 是否被持有: {}, 是否被當前線程持有: {}", isLocked, isHeldByCurrentThread);

                // 3. 強制解鎖（模擬異常情況下的清理）
                boolean forceUnlocked = lockManager.forceUnlock(lockResource);
                log.info("強制解鎖結果: {}", forceUnlocked);

                // 4. 檢查解鎖後的狀態
                boolean isLockedAfterForceUnlock = lockManager.isLocked(lockResource);
                log.info("強制解鎖後狀態 - 是否被持有: {}", isLockedAfterForceUnlock);

                result.put("success", true);
                result.put("message", "強制解鎖示範完成");
                result.put("lockAcquired", true);
                result.put("initialLocked", isLocked);
                result.put("heldByCurrentThread", isHeldByCurrentThread);
                result.put("forceUnlocked", forceUnlocked);
                result.put("lockedAfterForceUnlock", isLockedAfterForceUnlock);

            } else {
                result.put("success", false);
                result.put("message", "無法獲取鎖進行強制解鎖示範");
                log.warn("無法獲取鎖: {}", lockResource);
            }

        } catch (Exception e) {
            log.error("強制解鎖示範失敗", e);
            result.put("success", false);
            result.put("message", "強制解鎖示範失敗: " + e.getMessage());
        }

        return result;
    }

    // ==================== 私有輔助方法 ====================
    /**
     * 模擬訂單處理
     */
    private void processOrder() {
        log.info("處理訂單中...");
        try {
            Thread.sleep(1000); // 模擬處理時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("訂單處理完成");
    }

    /**
     * 模擬更新訂單狀態
     */
    private void updateOrderStatus() {
        log.info("更新訂單狀態中...");
        try {
            Thread.sleep(500); // 模擬處理時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("訂單狀態更新完成");
    }

    /**
     * 模擬隊列處理
     */
    private void processQueue() {
        log.info("按順序處理隊列中...");
        try {
            Thread.sleep(1500); // 模擬處理時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        log.info("隊列處理完成");
    }

    /**
     * 模擬讀取資料
     */
    private String readData() {
        log.info("讀取資料中...");
        try {
            Thread.sleep(800); // 模擬讀取時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        String data = "示範資料 - " + System.currentTimeMillis();
        log.info("資料讀取完成: {}", data);
        return data;
    }

    /**
     * 模擬寫入資料
     */
    private String writeData() {
        log.info("寫入資料中...");
        try {
            Thread.sleep(1200); // 模擬寫入時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        String data = "新資料 - " + System.currentTimeMillis();
        log.info("資料寫入完成: {}", data);
        return data;
    }

    /**
     * 模擬關鍵操作
     */
    private String performCriticalOperation() {
        log.info("執行關鍵操作中...");
        try {
            Thread.sleep(1000); // 模擬操作時間
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        String result = "關鍵操作結果 - " + System.currentTimeMillis();
        log.info("關鍵操作完成: {}", result);
        return result;
    }
}
