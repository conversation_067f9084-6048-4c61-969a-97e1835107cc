package com.nanshan.demo.cache.service;

import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.demo.cache.model.DemoUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.HashMap;

import java.util.ArrayList;

/**
 * Session 示範服務
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SessionDemoService {

    private final SessionManager sessionManager;
    private final JwtService jwtService;

    /**
     * 用戶登入
     */
    public String login(DemoUser user) {
        try {
            // 生成 JWT Token
            String token = jwtService.generateToken(user);
            String jwtId = jwtService.extractJwtId(token);

            // 創建 Session 對象
            SessionObject session = SessionObject.builder()
                    .userId(user.getId())
                    .clientId(user.getClientId())
                    .jwtId(jwtId)
                    .createdAt(LocalDateTime.now())
                    .lastActiveTime(LocalDateTime.now())
                    .build();

            // 保存 Session
            sessionManager.saveSession(jwtId, session, 3600); // 1小時

            log.info("User logged in successfully: userId={}, jwtId={}", user.getId(), jwtId);
            return token;
        } catch (Exception e) {
            log.error("Failed to login user: {}", user.getId(), e);
            throw new RuntimeException("Login failed", e);
        }
    }

    /**
     * 執行登入操作
     */
    public Map<String, Object> performLogin(String userId, String username, String clientId, String name) {
        try {
            DemoUser user = DemoUser.builder()
                    .userId(userId)
                    .username(username)
                    .clientId(clientId)
                    .name(name)
                    .enabled(true)
                    .createdAt(LocalDateTime.now())
                    .build();

            String token = login(user);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("token", token);
            result.put("message", "Login successful");
            return result;
        } catch (Exception e) {
            log.error("Failed to perform login", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Login failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 用戶登出
     */
    public boolean logout(String token) {
        try {
            String jwtId = jwtService.extractJwtId(token);
            boolean deleted = sessionManager.deleteSession(jwtId);

            if (deleted) {
                log.info("User logged out successfully: jwtId={}", jwtId);
            } else {
                log.warn("Session not found for logout: jwtId={}", jwtId);
            }

            return deleted;
        } catch (Exception e) {
            log.error("Failed to logout user", e);
            return false;
        }
    }

    /**
     * 執行登出操作
     */
    public Map<String, Object> performLogout(String token) {
        try {
            boolean success = logout(token);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "Logout successful" : "Session not found");
            return result;
        } catch (Exception e) {
            log.error("Failed to perform logout", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Logout failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 驗證 Session
     */
    public boolean validateSession(String token) {
        try {
            String jwtId = jwtService.extractJwtId(token);
            return sessionManager.existsSession(jwtId);
        } catch (Exception e) {
            log.error("Failed to validate session", e);
            return false;
        }
    }

    /**
     * 獲取 Session 信息
     */
    public Optional<SessionObject> getSessionObject(String token) {
        try {
            String jwtId = jwtService.extractJwtId(token);
            return sessionManager.loadSession(jwtId);
        } catch (Exception e) {
            log.error("Failed to get session", e);
            return Optional.empty();
        }
    }

    /**
     * 獲取 Session 信息（返回 Map）
     */
    public Map<String, Object> getSession(String jwtId) {
        try {
            Optional<SessionObject> sessionOpt = sessionManager.loadSession(jwtId);

            Map<String, Object> result = new HashMap<>();
            if (sessionOpt.isPresent()) {
                result.put("success", true);
                result.put("session", sessionOpt.get());
            } else {
                result.put("success", false);
                result.put("message", "Session not found");
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to get session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Get session failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 查詢 Session
     */
    public Map<String, Object> querySession(String token) {
        try {
            Optional<SessionObject> sessionOpt = getSessionObject(token);

            Map<String, Object> result = new HashMap<>();
            if (sessionOpt.isPresent()) {
                result.put("success", true);
                result.put("session", sessionOpt.get());
            } else {
                result.put("success", false);
                result.put("message", "Session not found");
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to query session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Query failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 續期 Session
     */
    public boolean renewSession(String token) {
        try {
            String jwtId = jwtService.extractJwtId(token);
            return sessionManager.renewSession(jwtId, 3600); // 續期1小時
        } catch (Exception e) {
            log.error("Failed to renew session", e);
            return false;
        }
    }

    /**
     * 續期 Session（帶 TTL 參數）
     */
    public Map<String, Object> renewSession(String jwtId, long ttlSeconds) {
        try {
            boolean success = sessionManager.renewSession(jwtId, ttlSeconds);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "Session renewed successfully" : "Failed to renew session");
            result.put("ttl", ttlSeconds);
            return result;
        } catch (Exception e) {
            log.error("Failed to renew session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Renew failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根據用戶ID查找所有 Session
     */
    public List<SessionObject> findSessionsByUserId(String userId) {
        try {
            return sessionManager.findSessionsByUserId(userId);
        } catch (Exception e) {
            log.error("Failed to find sessions by userId: {}", userId, e);
            return List.of();
        }
    }

    /**
     * 查詢用戶 Sessions
     */
    public Map<String, Object> queryUserSessions(String userId) {
        try {
            List<SessionObject> sessions = findSessionsByUserId(userId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("sessions", sessions);
            result.put("count", sessions.size());
            return result;
        } catch (Exception e) {
            log.error("Failed to query user sessions", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Query failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 根據客戶端ID查找所有 Session
     */
    public List<SessionObject> findSessionsByClientId(String clientId) {
        try {
            return sessionManager.findSessionsByClientId(clientId);
        } catch (Exception e) {
            log.error("Failed to find sessions by clientId: {}", clientId, e);
            return List.of();
        }
    }

    /**
     * 批量操作
     */
    public Map<String, Object> batchOperations(List<String> userIds) {
        try {
            Map<String, Object> result = new HashMap<>();
            List<Map<String, Object>> operations = new ArrayList<>();

            for (String userId : userIds) {
                Map<String, Object> operation = new HashMap<>();
                operation.put("userId", userId);

                List<SessionObject> sessions = findSessionsByUserId(userId);
                operation.put("sessionCount", sessions.size());

                operations.add(operation);
            }

            result.put("success", true);
            result.put("operations", operations);
            return result;
        } catch (Exception e) {
            log.error("Failed to perform batch operations", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Batch operations failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 刪除用戶的所有 Session
     */
    public long deleteAllUserSessions(String userId) {
        try {
            long deletedCount = sessionManager.deleteSessionsByUserId(userId);
            log.info("Deleted {} sessions for userId: {}", deletedCount, userId);
            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to delete sessions for userId: {}", userId, e);
            return 0;
        }
    }

    /**
     * 刪除 Session（通過 Token）
     */
    public boolean deleteSessionByToken(String token) {
        return logout(token);
    }

    /**
     * 刪除 Session（返回 Map）
     */
    public Map<String, Object> deleteSession(String jwtId) {
        try {
            boolean success = sessionManager.deleteSession(jwtId);

            Map<String, Object> result = new HashMap<>();
            result.put("success", success);
            result.put("message", success ? "Session deleted successfully" : "Session not found");
            return result;
        } catch (Exception e) {
            log.error("Failed to delete session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Delete failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 創建示範 Session（返回 Token）
     */
    public String createDemoSessionToken() {
        DemoUser demoUser = DemoUser.builder()
                .userId("demo-user-" + System.currentTimeMillis())
                .username("demo")
                .clientId("demo-client")
                .name("Demo User")
                .enabled(true)
                .createdAt(LocalDateTime.now())
                .build();

        return login(demoUser);
    }

    /**
     * 創建示範 Session（返回 Map）
     */
    public Map<String, Object> createDemoSession() {
        try {
            String token = createDemoSessionToken();
            String jwtId = jwtService.extractJwtId(token);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("token", token);
            result.put("jwtId", jwtId);
            result.put("message", "Demo session created successfully");
            return result;
        } catch (Exception e) {
            log.error("Failed to create demo session", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Create demo session failed: " + e.getMessage());
            return result;
        }
    }

    /**
     * 獲取 Session 統計信息
     */
    public SessionManager.SessionStats getSessionStatsObject() {
        try {
            return sessionManager.getSessionStats();
        } catch (Exception e) {
            log.error("Failed to get session stats", e);
            return null;
        }
    }

    /**
     * 獲取 Session 統計信息（返回 Map）
     */
    public Map<String, Object> getSessionStats() {
        try {
            SessionManager.SessionStats stats = sessionManager.getSessionStats();

            Map<String, Object> result = new HashMap<>();
            if (stats != null) {
                result.put("success", true);
                result.put("totalSessions", stats.getTotalSessions());
                result.put("activeSessions", stats.getActiveSessions());
                result.put("expiredSessions", stats.getExpiredSessions());
            } else {
                result.put("success", false);
                result.put("message", "Failed to get session stats");
            }
            return result;
        } catch (Exception e) {
            log.error("Failed to get session stats", e);
            Map<String, Object> result = new HashMap<>();
            result.put("success", false);
            result.put("message", "Get stats failed: " + e.getMessage());
            return result;
        }
    }
}
