# Demo Common Cache 生產環境配置

spring:
  # Redis 基本配置 - 生產環境
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:0}
      username: ${REDIS_USERNAME:}         # Redis 用戶名（從環境變數讀取）
      password: ${REDIS_PASSWORD:}         # Redis 密碼（從環境變數讀取）
      timeout: 5000ms                     # 生產環境較長的超時時間

# Common Cache 生產環境配置
common:
  cache:
    # Redis 連線進階配置 - 生產環境優化
    redis:
      connection-pool:
        pool-size: 64                     # 生產環境使用較大的連線池
        minimum-idle-size: 10             # 最小空閒連線數
        idle-connection-timeout: 10000    # 空閒連線超時時間（毫秒）
      timeout:
        connect-timeout: 10000            # 連線超時時間（毫秒）
        command-timeout: 5000             # 生產環境較長的命令超時時間
      retry:
        attempts: 5                       # 生產環境更多重試次數
        interval: 2000                    # 重試間隔（毫秒）
      thread-pool:
        threads: 16                       # 生產環境使用更多執行緒
        netty-threads: 32                 # Netty 執行緒數
      misc:
        keep-alive: true                  # 是否啟用 Keep Alive
        tcp-no-delay: true                # 是否啟用 TCP No Delay
    
    # Redis 環境保護設定 - 生產環境
    env-guard:
      enabled: true
      production-profiles: 
        - prod
        - production
      dangerous-operations-enabled: false # 生產環境禁止危險操作
    
    # Session 管理配置 - 生產環境
    session:
      default-ttl: 3600                   # 生產環境 1 小時 TTL
      key-prefix: "prod:session"
      cleanup-interval: 600               # 10 分鐘清理一次
      auto-renewal: true
      renewal-threshold: 0.1              # 剩餘 10% TTL 時自動續期
    
    # 快取管理配置 - 生產環境
    cache:
      default-ttl: 7200                   # 生產環境 2 小時 TTL
      key-prefix: "prod:cache"
      auto-renewal: true
      renewal-threshold: 0.1              # 剩餘 10% TTL 時自動續期
      max-size: 50000                     # 生產環境更大的快取容量
    
    # 鎖管理配置 - 生產環境
    lock:
      default-lease-time: 60              # 生產環境較長的租約時間
      default-wait-time: 30               # 生產環境較長的等待時間
      key-prefix: "prod:lock"
      fair-lock-enabled: true
    
    # 清理器配置 - 生產環境
    cleaner:
      enabled: true
      schedule-interval: 600              # 10 分鐘執行一次
      batch-size: 2000                    # 生產環境更大的批次大小
      expired-key-scan-count: 200         # 更多的過期 Key 掃描數量

# 日誌配置 - 生產環境
logging:
  level:
    com.nanshan: INFO                     # 生產環境使用 INFO 級別
    org.redisson: WARN
    org.springframework.data.redis: WARN
    io.lettuce.core: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/demo-common-cache.log
    max-size: 100MB
    max-history: 30
