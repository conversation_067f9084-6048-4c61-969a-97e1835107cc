# Demo Common Cache 應用程式配置
# 展示 common-cache 最新功能和最佳實踐

server:
  port: 8080                              # 改用 8084 端口避免衝突
  servlet:
    context-path: /

spring:
  application:
    name: demo-common-cache

  profiles:
    active: dev                           # 預設使用開發環境

  # Redis 基本配置（必須配置）
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      username:                           # Redis 用戶名（Redis 6.0+ 支援，可選）
      password:                           # Redis 密碼（如果有的話）
      timeout: 3000ms                     # 基本連線超時時間

  # Jackson 序列化配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Taipei
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false



# Actuator 監控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
  endpoint:
    health:
      show-details: always

# Common Cache 配置（展示最新配置結構）
common:
  cache:
    # 基本配置（必須）
    session:
      default-ttl: 1800                   # Session TTL: 30 分鐘
      auto-renewal: true                  # 啟用自動續期
      renewal-threshold: 0.2              # 剩餘 20% TTL 時自動續期
      key-prefix: "demo:session"          # Session 鍵前綴
    cache:
      default-ttl: 3600                   # Cache TTL: 1 小時
      auto-renewal: true                  # 啟用自動續期
      max-size: 10000                     # 最大快取大小
      key-prefix: "demo:cache"            # Cache 鍵前綴
    lock:
      default-lease-time: 30              # 鎖租約時間: 30 秒
      default-wait-time: 10               # 鎖等待時間: 10 秒
      fair-lock-enabled: true             # 啟用公平鎖
      key-prefix: "demo:lock"             # Lock 鍵前綴
    cleaner:
      enabled: true                       # 啟用清理器
      schedule-interval: 300              # 清理間隔: 5 分鐘（Demo 環境更頻繁）
      statistics-enabled: true            # 啟用統計

    # 進階配置（可選）
    redis:
      connection-pool:
        pool-size: 32                     # Demo 環境使用較小的連線池
        minimum-idle-size: 5              # 最小空閒連線數
        idle-connection-timeout: 10000    # 空閒連線超時時間（毫秒）
      timeout:
        connect-timeout: 10000            # 連線超時時間（毫秒）
        command-timeout: 3000             # 命令執行超時時間（毫秒）
      retry:
        attempts: 3                       # 重試次數
        interval: 1500                    # 重試間隔（毫秒）
      # 專家配置（調優）
      thread-pool:
        threads: 8                        # Demo 環境使用較少執行緒
        netty-threads: 16                 # Netty 執行緒數
      misc:
        keep-alive: true                  # 啟用 Keep Alive
        tcp-no-delay: true                # 啟用 TCP No Delay

    # 環境保護配置
    env-guard:
      enabled: true                       # 啟用環境保護
      production-profiles:
        - prod
        - production
      dangerous-operations-enabled: true  # Demo 環境允許危險操作

# JWT 配置（Demo 專用）
demo:
  jwt:
    secret: "demo-secret-key-for-common-cache-testing-only-do-not-use-in-production"
    expiration: 3600                      # JWT Token 過期時間（秒）
    issuer: "demo-common-cache"           # JWT 發行者

  # Demo 應用配置
  app:
    name: "Demo Common Cache"
    version: "1.0.0-SNAPSHOT"
    description: "展示 common-cache 最新功能的示範應用"

# SpringDoc OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs                        # API 文檔 JSON 路徑
    enabled: true
  swagger-ui:
    path: /swagger-ui.html                  # Swagger UI 路徑
    enabled: true
    try-it-out-enabled: true                # 啟用 "Try it out" 功能
    operations-sorter: method               # 按 HTTP 方法排序
    tags-sorter: alpha                      # 按字母順序排序標籤
    doc-expansion: none                     # 預設不展開文檔
    disable-swagger-default-url: true       # 禁用預設 URL
  show-actuator: true                       # 顯示 Actuator 端點

# 日誌配置（詳細配置請參考 logback-spring.xml）
logging:
  config: classpath:logback-spring.xml
