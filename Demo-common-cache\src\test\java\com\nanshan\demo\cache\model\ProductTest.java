package com.nanshan.demo.cache.model;

import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Product 模型測試
 * 
 * 測試 Product 類的基本功能，不需要外部依賴
 */
public class ProductTest {

    @Test
    public void testProductCreation() {
        // 測試 Product 建構
        Product product = Product.builder()
                .productId("PROD-001")
                .name("測試產品")
                .description("這是一個測試產品")
                .price(new BigDecimal("99.99"))
                .stock(100)
                .category("電子產品")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 驗證基本屬性
        assertNotNull(product);
        assertEquals("PROD-001", product.getProductId());
        assertEquals("測試產品", product.getName());
        assertEquals("這是一個測試產品", product.getDescription());
        assertEquals(new BigDecimal("99.99"), product.getPrice());
        assertEquals(100, product.getStock());
        assertEquals("電子產品", product.getCategory());
        assertNotNull(product.getCreatedAt());
        assertNotNull(product.getUpdatedAt());
    }

    @Test
    public void testProductEquality() {
        LocalDateTime now = LocalDateTime.now();
        
        Product product1 = Product.builder()
                .productId("PROD-001")
                .name("測試產品")
                .description("描述")
                .price(new BigDecimal("99.99"))
                .stock(100)
                .category("電子產品")
                .createdAt(now)
                .updatedAt(now)
                .build();

        Product product2 = Product.builder()
                .productId("PROD-001")
                .name("測試產品")
                .description("描述")
                .price(new BigDecimal("99.99"))
                .stock(100)
                .category("電子產品")
                .createdAt(now)
                .updatedAt(now)
                .build();

        // 測試相等性
        assertEquals(product1, product2);
        assertEquals(product1.hashCode(), product2.hashCode());
    }

    @Test
    public void testProductToString() {
        Product product = Product.builder()
                .productId("PROD-001")
                .name("測試產品")
                .build();

        String productString = product.toString();
        
        // 驗證 toString 包含關鍵信息
        assertNotNull(productString);
        assertTrue(productString.contains("PROD-001"));
        assertTrue(productString.contains("測試產品"));
    }

    @Test
    public void testProductBuilder() {
        // 測試 Builder 模式
        Product.ProductBuilder builder = Product.builder();
        
        assertNotNull(builder);
        
        Product product = builder
                .productId("PROD-002")
                .name("Builder 測試產品")
                .price(new BigDecimal("199.99"))
                .stock(50)
                .build();

        assertEquals("PROD-002", product.getProductId());
        assertEquals("Builder 測試產品", product.getName());
        assertEquals(new BigDecimal("199.99"), product.getPrice());
        assertEquals(50, product.getStock());
    }

    @Test
    public void testProductSetters() {
        Product product = new Product();
        
        // 測試 setter 方法
        product.setProductId("PROD-003");
        product.setName("Setter 測試產品");
        product.setDescription("使用 setter 設置的產品");
        product.setPrice(new BigDecimal("299.99"));
        product.setStock(75);
        product.setCategory("測試類別");
        
        LocalDateTime now = LocalDateTime.now();
        product.setCreatedAt(now);
        product.setUpdatedAt(now);

        // 驗證 setter 效果
        assertEquals("PROD-003", product.getProductId());
        assertEquals("Setter 測試產品", product.getName());
        assertEquals("使用 setter 設置的產品", product.getDescription());
        assertEquals(new BigDecimal("299.99"), product.getPrice());
        assertEquals(75, product.getStock());
        assertEquals("測試類別", product.getCategory());
        assertEquals(now, product.getCreatedAt());
        assertEquals(now, product.getUpdatedAt());
    }

    @Test
    public void testProductValidation() {
        // 測試邊界值
        Product product = Product.builder()
                .productId("")
                .name("")
                .price(BigDecimal.ZERO)
                .stock(0)
                .build();

        // 驗證空值處理
        assertEquals("", product.getProductId());
        assertEquals("", product.getName());
        assertEquals(BigDecimal.ZERO, product.getPrice());
        assertEquals(0, product.getStock());
    }

    @Test
    public void testProductNullValues() {
        // 測試 null 值處理
        Product product = Product.builder()
                .productId(null)
                .name(null)
                .description(null)
                .price(null)
                .category(null)
                .createdAt(null)
                .updatedAt(null)
                .build();

        // 驗證 null 值
        assertNull(product.getProductId());
        assertNull(product.getName());
        assertNull(product.getDescription());
        assertNull(product.getPrice());
        assertNull(product.getCategory());
        assertNull(product.getCreatedAt());
        assertNull(product.getUpdatedAt());
    }

    @Test
    public void testProductCopy() {
        LocalDateTime now = LocalDateTime.now();
        
        Product original = Product.builder()
                .productId("PROD-004")
                .name("原始產品")
                .description("原始描述")
                .price(new BigDecimal("399.99"))
                .stock(25)
                .category("原始類別")
                .createdAt(now)
                .updatedAt(now)
                .build();

        // 創建副本
        Product copy = Product.builder()
                .productId(original.getProductId())
                .name(original.getName())
                .description(original.getDescription())
                .price(original.getPrice())
                .stock(original.getStock())
                .category(original.getCategory())
                .createdAt(original.getCreatedAt())
                .updatedAt(original.getUpdatedAt())
                .build();

        // 驗證副本
        assertEquals(original, copy);
        assertNotSame(original, copy); // 確保是不同的對象實例
    }

    @Test
    public void testProductModification() {
        Product product = Product.builder()
                .productId("PROD-005")
                .name("可修改產品")
                .stock(100)
                .build();

        // 修改產品
        product.setName("已修改產品");
        product.setStock(150);
        product.setUpdatedAt(LocalDateTime.now());

        // 驗證修改
        assertEquals("已修改產品", product.getName());
        assertEquals(150, product.getStock());
        assertNotNull(product.getUpdatedAt());
    }
}
