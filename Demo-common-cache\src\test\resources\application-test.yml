# Demo Common Cache 測試環境配置
# 適用於獨立環境的測試配置

server:
  port: ${TEST_SERVER_PORT:8082}  # 避免與其他服務衝突

spring:
  profiles:
    active: test
  application:
    name: demo-common-cache-test
  
  # Redis 基本配置 - 支援環境變數
  data:
    redis:
      host: ${TEST_REDIS_HOST:localhost}
      port: ${TEST_REDIS_PORT:6379}
      database: ${TEST_REDIS_DATABASE:15}  # 使用高編號資料庫避免衝突
      username: ${TEST_REDIS_USERNAME:}
      password: ${TEST_REDIS_PASSWORD:}
      timeout: ${TEST_REDIS_TIMEOUT:10000ms}  # 獨立環境可能延遲較高
      
      # 連接池配置 - 測試環境
      lettuce:
        pool:
          max-active: ${TEST_REDIS_POOL_MAX_ACTIVE:8}
          max-idle: ${TEST_REDIS_POOL_MAX_IDLE:4}
          min-idle: ${TEST_REDIS_POOL_MIN_IDLE:1}
          max-wait: ${TEST_REDIS_POOL_MAX_WAIT:5000ms}
        shutdown-timeout: ${TEST_REDIS_SHUTDOWN_TIMEOUT:2000ms}

  # Jackson 序列化配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Taipei
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# Common Cache 測試配置 - 獨立環境適配
common:
  cache:
    # Redis 連接配置 - 獨立環境
    redis:
      host: ${TEST_REDIS_HOST:localhost}
      port: ${TEST_REDIS_PORT:6379}
      database: ${TEST_REDIS_DATABASE:15}
      username: ${TEST_REDIS_USERNAME:}
      password: ${TEST_REDIS_PASSWORD:}
      timeout: ${TEST_REDIS_TIMEOUT:10000}
      
      # 連接池配置 - 測試環境優化
      connection-pool:
        pool-size: ${TEST_REDIS_POOL_SIZE:8}
        minimum-idle-size: ${TEST_REDIS_POOL_MIN_IDLE:1}
        idle-connection-timeout: ${TEST_REDIS_IDLE_TIMEOUT:15000}
        connection-timeout: ${TEST_REDIS_CONNECT_TIMEOUT:10000}
      
      # 重試配置 - 獨立環境可能不穩定
      retry:
        attempts: ${TEST_REDIS_RETRY_ATTEMPTS:5}
        interval: ${TEST_REDIS_RETRY_INTERVAL:2000}
      
      # 線程池配置 - 測試環境
      thread-pool:
        threads: ${TEST_REDIS_THREADS:4}
        netty-threads: ${TEST_REDIS_NETTY_THREADS:8}
    
    # Session 管理配置 - 測試環境
    session:
      enabled: true
      default-time-to-live: ${TEST_SESSION_TTL:300}  # 5分鐘，測試用較短時間
      key-prefix: ${TEST_SESSION_PREFIX:test:demo:session}
      cleanup-interval: ${TEST_SESSION_CLEANUP:60}  # 1分鐘清理一次
      auto-renewal: ${TEST_SESSION_AUTO_RENEWAL:true}
      renewal-threshold: ${TEST_SESSION_RENEWAL_THRESHOLD:0.3}
    
    # 快取管理配置 - 測試環境
    cache:
      enabled: true
      default-time-to-live: ${TEST_CACHE_TTL:600}  # 10分鐘
      key-prefix: ${TEST_CACHE_PREFIX:test:demo:cache}
      auto-renewal: ${TEST_CACHE_AUTO_RENEWAL:true}
      renewal-threshold: ${TEST_CACHE_RENEWAL_THRESHOLD:0.2}
      max-size: ${TEST_CACHE_MAX_SIZE:500}  # 測試環境較小的快取容量
    
    # 分散式鎖配置 - 測試環境
    lock:
      enabled: true
      default-wait-time: ${TEST_LOCK_WAIT_TIME:10}  # 測試環境等待時間
      default-lease-time: ${TEST_LOCK_LEASE_TIME:20}  # 測試環境租約時間
      key-prefix: ${TEST_LOCK_PREFIX:test:demo:lock}
    
    # 清理器配置 - 測試環境
    cleaner:
      enabled: ${TEST_CLEANER_ENABLED:true}
      schedule-interval: ${TEST_CLEANER_INTERVAL:120}  # 2分鐘清理一次
      batch-size: ${TEST_CLEANER_BATCH_SIZE:50}  # 較小的批次大小
      expired-key-scan-count: ${TEST_CLEANER_SCAN_COUNT:25}
    
    # 環境保護配置 - 測試環境
    env-guard:
      enabled: true
      dangerous-operations-enabled: ${TEST_ALLOW_DANGEROUS_OPS:true}  # 測試環境允許
      production-profiles: 
        - prod
        - production
        - staging

# 測試專用配置
test:
  # 環境檢查配置
  environment:
    check-redis-connection: ${TEST_CHECK_REDIS:true}
    check-pubsub-permissions: ${TEST_CHECK_PUBSUB:true}
    check-script-permissions: ${TEST_CHECK_SCRIPT:true}
    graceful-degradation: ${TEST_GRACEFUL_DEGRADATION:true}
    skip-environment-check: ${TEST_SKIP_ENV_CHECK:false}
  
  # 測試超時配置 - 獨立環境可能較慢
  timeouts:
    connection-test: ${TEST_CONNECTION_TIMEOUT:15000}
    operation-test: ${TEST_OPERATION_TIMEOUT:10000}
    lock-test: ${TEST_LOCK_TIMEOUT:20000}
    cleanup-test: ${TEST_CLEANUP_TIMEOUT:30000}
    demo-test: ${TEST_DEMO_TIMEOUT:45000}
  
  # 測試重試配置
  retry:
    max-attempts: ${TEST_RETRY_ATTEMPTS:3}
    delay-between-attempts: ${TEST_RETRY_DELAY:2000}
    exponential-backoff: ${TEST_EXPONENTIAL_BACKOFF:true}
  
  # 測試數據配置
  data:
    cleanup-on-startup: ${TEST_CLEANUP_ON_STARTUP:true}
    cleanup-on-shutdown: ${TEST_CLEANUP_ON_SHUTDOWN:true}
    use-unique-keys: ${TEST_USE_UNIQUE_KEYS:true}

# 日誌配置 - 測試環境
logging:
  level:
    '[com.nanshan.common.cache]': ${TEST_LOG_LEVEL_CACHE:INFO}
    '[com.nanshan.demo.cache]': ${TEST_LOG_LEVEL_DEMO:INFO}
    '[org.redisson]': ${TEST_LOG_LEVEL_REDISSON:WARN}
    '[org.springframework.data.redis]': ${TEST_LOG_LEVEL_REDIS:WARN}
    '[org.springframework.test]': ${TEST_LOG_LEVEL_TEST:INFO}
    '[org.springframework.boot.test]': ${TEST_LOG_LEVEL_BOOT_TEST:INFO}
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n"
  file:
    name: ${TEST_LOG_FILE:logs/demo-test.log}

# Actuator 配置 - 測試監控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,env
      base-path: /test-actuator
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    redis:
      enabled: true
    defaults:
      enabled: true
  server:
    port: ${TEST_ACTUATOR_PORT:8083}

# 測試環境特殊配置
---
spring:
  config:
    activate:
      on-profile: test-standalone

# 獨立環境專用配置覆蓋
test:
  environment:
    graceful-degradation: true
    skip-environment-check: false
  timeouts:
    connection-test: 20000
    operation-test: 15000
    lock-test: 30000
  retry:
    max-attempts: 5
    delay-between-attempts: 3000

common:
  cache:
    redis:
      retry:
        attempts: 5
        interval: 3000
    session:
      default-time-to-live: 600  # 獨立環境較長 TTL
    cache:
      default-time-to-live: 1200
    lock:
      default-wait-time: 15  # 獨立環境較長等待時間
      default-lease-time: 30

logging:
  level:
    '[com.nanshan.common.cache]': DEBUG
    '[com.nanshan.demo.cache]': DEBUG
