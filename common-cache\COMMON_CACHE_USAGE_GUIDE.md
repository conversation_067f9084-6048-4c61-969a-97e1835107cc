# Common Cache 使用建議書

## 組件選擇建議

| 功能需求 | 推薦組件 | 使用場景 |
|----------|----------|----------|
| 用戶會話管理 | SessionManager | Web 應用、API 認證 |
| 數據快取 | CacheManager | 熱點數據、查詢結果快取 |
| 併發控制 | LockManager | 庫存扣減、訂單處理 |
| 數據清理 | RedisCleaner | 長期運行的系統 |
| 環境保護 | EnvGuard | 生產環境部署 |

## ⚙️ 配置

### 1. 環境配置策略

#### 開發環境配置
```yaml
common:
  cache:
    env-guard:
      dangerous-operations-enabled: true    # 允許危險操作便於調試
    session:
      default-time-to-live: 600           # 較短 TTL 便於測試
      key-prefix: "dev:session"           # 環境隔離
    cache:
      key-prefix: "dev:cache"
    cleaner:
      schedule-interval: 300               # 更頻繁清理
    redis:
      connection-pool:
        pool-size: 8                       # 較小連接池
```

#### 測試環境配置
```yaml
common:
  cache:
    env-guard:
      dangerous-operations-enabled: false  # 模擬生產環境
    session:
      default-time-to-live: 1800
      key-prefix: "test:session"
    cache:
      key-prefix: "test:cache"
    cleaner:
      schedule-interval: 600
    redis:
      connection-pool:
        pool-size: 16
```

#### 生產環境配置
```yaml
common:
  cache:
    env-guard:
      dangerous-operations-enabled: false  # 禁止危險操作
    session:
      default-time-to-live: 3600          # 較長 TTL
      key-prefix: "prod:session"
    cache:
      key-prefix: "prod:cache"
      max-size: 100000                    # 更大快取容量
    cleaner:
      schedule-interval: 1800             # 較少清理頻率
    redis:
      connection-pool:
        pool-size: 64                     # 更大連接池
        minimum-idle-size: 16
      thread-pool:
        threads: 32
```

## 💡 使用

### 1. Session 管理模式

#### 標準 Web 應用模式
```java
@Service
public class UserAuthService {
    
    @Autowired
    private SessionManager sessionManager;
    
    // 用戶登錄
    public String login(String username, String password) {
        // 驗證用戶
        UserInfo user = validateUser(username, password);
        
        // 創建 Session
        String sessionId = sessionManager.createSession(
            user.getId(), 
            user, 
            3600  // 1小時過期
        );
        
        return sessionId;
    }
    
    // 獲取當前用戶
    public UserInfo getCurrentUser(String sessionId) {
        return sessionManager.getSession(sessionId, UserInfo.class);
    }
    
    // 用戶登出
    public void logout(String sessionId) {
        sessionManager.deleteSession(sessionId);
    }
}
```

#### JWT Token 管理模式
```java
@Service
public class JwtTokenService {
    
    @Autowired
    private SessionManager sessionManager;
    
    public String generateToken(UserInfo user) {
        // 生成 JWT Token
        String jwtToken = jwtUtil.generateToken(user);
        
        // 將 Token 存儲到 Session 中
        sessionManager.createSession(
            jwtToken, 
            user, 
            Duration.ofHours(24)
        );
        
        return jwtToken;
    }
    
    public boolean validateToken(String token) {
        return sessionManager.exists(token);
    }
}
```

### 2. 快取管理模式

#### 查詢結果快取模式
```java
@Service
public class UserService {
    
    @Autowired
    private CacheManager cacheManager;
    
    @Autowired
    private UserRepository userRepository;
    
    public UserInfo getUserById(Long userId) {
        String cacheKey = "user:" + userId;
        
        // 先從快取獲取
        UserInfo user = cacheManager.get(cacheKey, UserInfo.class);
        if (user != null) {
            return user;
        }
        
        // 快取未命中，從數據庫查詢
        user = userRepository.findById(userId);
        if (user != null) {
            // 存入快取，1小時過期
            cacheManager.set(cacheKey, user, 3600);
        }
        
        return user;
    }
    
    public void updateUser(UserInfo user) {
        // 更新數據庫
        userRepository.save(user);
        
        // 清除快取
        String cacheKey = "user:" + user.getId();
        cacheManager.delete(cacheKey);
    }
}
```

#### 批量快取模式
```java
@Service
public class ProductService {
    
    @Autowired
    private CacheManager cacheManager;
    
    public Map<Long, Product> getProductsByIds(List<Long> productIds) {
        // 構建快取 Key
        Map<String, Long> keyToIdMap = productIds.stream()
            .collect(Collectors.toMap(
                id -> "product:" + id,
                id -> id
            ));
        
        // 批量獲取快取
        Map<String, Product> cachedProducts = cacheManager.getMultiple(
            keyToIdMap.keySet(), 
            Product.class
        );
        
        // 找出未命中的 ID
        Set<Long> missedIds = keyToIdMap.entrySet().stream()
            .filter(entry -> !cachedProducts.containsKey(entry.getKey()))
            .map(Map.Entry::getValue)
            .collect(Collectors.toSet());
        
        // 從數據庫查詢未命中的數據
        if (!missedIds.isEmpty()) {
            List<Product> dbProducts = productRepository.findByIdIn(missedIds);
            
            // 批量存入快取
            Map<String, Object> toCache = dbProducts.stream()
                .collect(Collectors.toMap(
                    product -> "product:" + product.getId(),
                    product -> product
                ));
            
            cacheManager.setMultiple(toCache, 3600);
            
            // 合併結果
            dbProducts.forEach(product -> 
                cachedProducts.put("product:" + product.getId(), product)
            );
        }
        
        // 轉換回 ID -> Product 映射
        return cachedProducts.entrySet().stream()
            .collect(Collectors.toMap(
                entry -> keyToIdMap.get(entry.getKey()),
                Map.Entry::getValue
            ));
    }
}
```

### 3. 分散式鎖模式

#### 庫存扣減模式
```java
@Service
public class InventoryService {
    
    @Autowired
    private LockManager lockManager;
    
    public boolean deductInventory(Long productId, int quantity) {
        String lockKey = "inventory:" + productId;
        
        return lockManager.executeWithLock(lockKey, () -> {
            // 查詢當前庫存
            int currentStock = getStock(productId);
            
            if (currentStock >= quantity) {
                // 扣減庫存
                updateStock(productId, currentStock - quantity);
                return true;
            } else {
                return false;
            }
        }, 10, 30, TimeUnit.SECONDS);
    }
}
```

#### 訂單處理模式
```java
@Service
public class OrderService {
    
    @Autowired
    private LockManager lockManager;
    
    public void processOrder(Order order) {
        String lockKey = "order:" + order.getId();
        
        boolean acquired = lockManager.tryLock(
            lockKey, 
            10,  // 等待 10 秒
            60,  // 持有 60 秒
            TimeUnit.SECONDS
        );
        
        if (acquired) {
            try {
                // 處理訂單邏輯
                validateOrder(order);
                deductInventory(order);
                createPayment(order);
                updateOrderStatus(order);
                
            } finally {
                lockManager.unlock(lockKey, LockManager.LockType.REENTRANT);
            }
        } else {
            throw new BusinessException("訂單正在處理中，請稍後再試");
        }
    }
}
```

## 🔑 Redis Key

### RedisKeyHelper 工具類

Common Cache 提供了 `RedisKeyHelper` 工具類來統一管理 Redis Key 的命名規則，確保 Key 的一致性和可維護性。

#### 基本使用原則

```java
import com.nanshan.common.cache.util.RedisKeyHelper;

// ✅ 正確：使用 RedisKeyHelper
String sessionKey = RedisKeyHelper.buildSessionKey("web", "user123");

// ❌ 錯誤：手動字符串拼接
String sessionKey = "session:web:user123";
```

#### 不同類型 Key 的建構方式

```java
// 1. Session Key 建構
String sessionKey = RedisKeyHelper.buildSessionKey("clientId", "userId");
// 結果: "session:clientId:userId"

String jwtSessionKey = RedisKeyHelper.buildSessionKeyByJwtId("jwt-token-id");
// 結果: "session:jwt:jwt-token-id"

// 2. Cache Key 建構
String cacheKey = RedisKeyHelper.buildCacheKey("user", "123");
// 結果: "cache:user:123"

String multiLevelCacheKey = RedisKeyHelper.buildCacheKey("user", "profile", "123");
// 結果: "cache:user:profile:123"

// 3. Lock Key 建構
String lockKey = RedisKeyHelper.buildLockKey("order-process-456");
// 結果: "lock:order-process-456"

String typedLockKey = RedisKeyHelper.buildLockKey("order", "process-456");
// 結果: "lock:order:process-456"

// 4. 通用 Key 建構
String customKey = RedisKeyHelper.buildKey("app", "module", "function", "id");
// 結果: "app:module:function:id"
```

#### Key 檢查和解析

```java
// Key 類型檢查
boolean isSessionKey = RedisKeyHelper.isSessionKey("session:web:user123");
boolean isCacheKey = RedisKeyHelper.isCacheKey("cache:user:123");
boolean isLockKey = RedisKeyHelper.isLockKey("lock:order:456");

// Key 解析
String prefix = RedisKeyHelper.extractPrefix("session:web:user123");  // "session"
String userId = RedisKeyHelper.extractPart("session:web:user123", 2); // "user123"

// 模式 Key 建構（用於批量操作）
String sessionPattern = RedisKeyHelper.buildPatternKey("session", "*");
// 結果: "session:*"
```

#### 在業務代碼中的應用

```java
@Service
public class UserService {

    @Autowired
    private SessionManager sessionManager;

    @Autowired
    private CacheManager cacheManager;

    @Autowired
    private LockManager lockManager;

    public void updateUserProfile(String userId, UserProfile profile) {
        // 使用 RedisKeyHelper 建構鎖 Key
        String lockKey = RedisKeyHelper.buildLockKey("user", "update", userId);

        boolean acquired = lockManager.tryLock(lockKey, 10, 30, TimeUnit.SECONDS);
        if (acquired) {
            try {
                // 更新用戶資料
                updateUserInDatabase(userId, profile);

                // 使用 RedisKeyHelper 建構快取 Key
                String cacheKey = RedisKeyHelper.buildCacheKey("user", "profile", userId);
                cacheManager.delete(cacheKey);  // 清除快取

                // 更新相關 Session
                String sessionPattern = RedisKeyHelper.buildPatternKey("session", "*");
                // 處理相關 Session 更新...

            } finally {
                lockManager.unlock(lockKey, LockManager.LockType.REENTRANT);
            }
        }
    }

    public UserProfile getUserProfile(String userId) {
        // 使用 RedisKeyHelper 建構快取 Key
        String cacheKey = RedisKeyHelper.buildCacheKey("user", "profile", userId);

        UserProfile profile = cacheManager.get(cacheKey, UserProfile.class);
        if (profile == null) {
            profile = loadUserProfileFromDatabase(userId);
            cacheManager.set(cacheKey, profile, 3600); // 快取 1 小時
        }

        return profile;
    }
}
```

#### Key 命名最佳實踐

1. **使用語義化的 Key 名稱**
   ```java
   // ✅ 好的命名
   String userCacheKey = RedisKeyHelper.buildCacheKey("user", "profile", userId);
   String orderLockKey = RedisKeyHelper.buildLockKey("order", "payment", orderId);

   // ❌ 不好的命名
   String key1 = RedisKeyHelper.buildKey("u", "p", userId);
   String key2 = RedisKeyHelper.buildKey("o", "pay", orderId);
   ```

2. **保持 Key 層級的一致性**
   ```java
   // ✅ 一致的層級結構
   RedisKeyHelper.buildCacheKey("user", "profile", userId);
   RedisKeyHelper.buildCacheKey("user", "settings", userId);
   RedisKeyHelper.buildCacheKey("user", "permissions", userId);

   // ❌ 不一致的層級結構
   RedisKeyHelper.buildCacheKey("user", userId);
   RedisKeyHelper.buildCacheKey("user", "profile", "settings", userId);
   ```

3. **避免 Key 過長**
   ```java
   // ✅ 適當的長度
   RedisKeyHelper.buildCacheKey("product", "detail", productId);

   // ❌ 過長的 Key
   RedisKeyHelper.buildKey("application", "module", "submodule", "function",
                          "subfunction", "detail", "cache", productId);
   ```

4. **使用統一的 ID 格式**
   ```java
   // ✅ 統一使用字符串 ID
   String userId = String.valueOf(user.getId());
   String cacheKey = RedisKeyHelper.buildCacheKey("user", userId);

   // ❌ 混合使用不同類型
   String cacheKey = RedisKeyHelper.buildKey("user", user.getId().toString());
   ```

## 📚 學習資源

### 官方文檔
- [Redis 官方文檔](https://redis.io/documentation)
- [Redisson 官方文檔](https://redisson.org/)
- [Spring Boot 官方文檔](https://spring.io/projects/spring-boot)

### 最佳實踐
- [Redis 最佳實踐](https://redis.io/docs/manual/patterns/)
- [分散式系統設計模式](https://microservices.io/patterns/)
- [快取設計模式](https://docs.microsoft.com/en-us/azure/architecture/patterns/cache-aside)

### 社區資源
- [Redis 中文社區](https://redis.cn/)
- [Spring Boot 中文文檔](https://springboot.io/)
- [技術博客和教程](https://www.baeldung.com/redis)
