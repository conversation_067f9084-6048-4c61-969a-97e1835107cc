# Common Cache

## 📖 介紹

Common Cache 是一個基於 Redisson 的 Spring Boot 快取元件，提供完整的 Redis 快取、Session 管理、分散式鎖和自動清理功能。該元件採用模組化設計，支援按需啟用功能，適用於各種規模的 Java 應用。

### 主要特性
- 🚀 **高效能**: 基於 Redisson 的高效能 Redis 客戶端
- 🔒 **分散式鎖**: 支援可重入鎖、公平鎖、讀寫鎖等多種鎖類型
- 💾 **智慧快取**: 自動續期、批次操作、模式匹配刪除
- 👥 **Session 管理**: 靈活的 Session 生命週期管理
- 🧹 **自動清理**: 智慧過期資料清理，防止記憶體洩漏
- 🛡️ **環境保護**: 生產環境安全保護機制
- ⚙️ **靈活配置**: 支援多環境配置和自訂參數

## 🏗️ 專案結構

```
common-cache/
├── src/main/java/com/nanshan/common/cache/
│   ├── annotation/                    # 註解定義
│   │   └── EnableRedisSupport.java    # Redis 支援啟用註解
│   ├── config/                        # 配置類別
│   │   ├── CommonCacheAutoConfiguration.java    # 自動配置
│   │   ├── CommonCacheProperties.java           # 配置屬性
│   │   └── RedissonConfig.java                  # Redisson 配置
│   ├── exception/                     # 例外類別
│   │   ├── CacheOperationException.java         # 快取操作例外
│   │   ├── LockOperationException.java          # 鎖操作例外
│   │   ├── RedisOperationException.java         # Redis 操作例外
│   │   └── SessionNotFoundException.java        # Session 未找到例外
│   ├── model/                         # 資料模型
│   │   ├── SessionObject.java                   # Session 物件
│   │   └── SessionObjectBuilder.java            # Session 建構器
│   ├── service/                       # 核心服務
│   │   ├── CacheManager.java                   # 快取管理介面
│   │   ├── LockManager.java                    # 鎖管理介面
│   │   ├── RedisCleaner.java                  # Redis 清理介面
│   │   ├── RedisDataAccessor.java              # Redis 資料存取介面
│   │   ├── RedisDiagnosticService.java         # Redis 診斷服務介面
│   │   ├── SessionManager.java                 # Session 管理介面
│   │   └── impl/                              # 介面實作
│   │       ├── CacheManagerImpl.java           # 快取管理實作
│   │       ├── LockManagerImpl.java            # 鎖管理實作
│   │       ├── RedisCleanerImpl.java           # Redis 清理實作
│   │       ├── RedisDataAccessorImpl.java      # Redis 資料存取實作
│   │       └── SessionManagerImpl.java         # Session 管理實作
│   └── util/                          # 工具類別
│       └── RedisKeyHelper.java                 # Redis Key 管理工具
├── src/main/resources/                 # 配置檔案
│   ├── application.yml                 # 預設配置
│   ├── application-example.yml         # 配置範例
│   └── META-INF/spring.factories      # Spring 自動配置
└── pom.xml                            # Maven 依賴管理
```

## ⚡ 主要功能

### 1. Session 管理 (SessionManager)
- 建立、載入、更新、刪除 Session
- 支援 TTL 自動過期和手動續期
- 根據使用者 ID 和客戶端 ID 查詢 Session
- 批量刪除和清理過期 Session
- Session 統計資訊和監控
- JWT Token 整合支援

### 2. 快取管理 (CacheManager)
- 基礎 CRUD 操作 (put, get, remove)
- TTL 管理和自動續期
- 批量操作 (multiPut, multiGet, multiRemove)
- 快取統計資訊 (命中率、請求次數等)
- 過期快取自動清理
- 支援 Duration 和 TimeUnit 時間設定

### 3. 分散式鎖 (LockManager)
- 可重入鎖 (Reentrant Lock) - 阻塞式和非阻塞式
- 公平鎖 (Fair Lock) - 先到先得機制
- 讀寫鎖 (Read-Write Lock) - 讀寫分離
- 鎖狀態查詢和監控
- 逾時和重試機制
- 強制解鎖功能

### 4. 自動清理 (RedisCleaner)
- 過期資料自動清理 (Session、Cache、Lock)
- 模式匹配刪除和批量刪除
- TTL 設定和移除
- 清理統計資訊和監控
- 手動清理觸發
- 清理結果追蹤

### 5. Redis 診斷 (RedisDiagnosticService)
- 基本連線測試
- PubSub 功能測試
- 分散式鎖功能測試
- 強制解鎖測試
- 完整診斷報告

### 6. Redis 連線管理 (RedisConnectionManager)
- 連線初始化和關閉
- 連線健康檢查
- 連線狀態監控
- 自動重連機制
- 連線統計資訊

## ⚙️ 配置範例

### 基礎配置 (application.yml)

```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      timeout: 3000ms
      password: your_password  # 可選

common:
  cache:
    # Redis 連線池配置
    redis:
      connection-pool:
        pool-size: 32
        minimum-idle-size: 8
      thread-pool:
        threads: 16
        queue-capacity: 1000
    
    # Session 管理配置
    session:
      enabled: true
      default-time-to-live: 1800      # 30分鐘
      key-prefix: "session"
      cleanup-interval: 300           # 5分鐘清理一次
      auto-renewal: true
      renewal-threshold: 0.3          # 剩餘30%時自動續期
    
    # 快取管理配置
    cache:
      enabled: true
      default-time-to-live: 3600      # 1小時
      key-prefix: "cache"
      auto-renewal: true
      renewal-threshold: 0.2          # 剩餘20%時自動續期
      max-size: 10000
    
    # 分散式鎖配置
    lock:
      enabled: true
      default-wait-time: 10           # 等待鎖的時間（秒）
      default-lease-time: 30          # 鎖的持有時間（秒）
      key-prefix: "lock"
    
    # 自動清理配置
    cleaner:
      enabled: true
      schedule-interval: 600          # 10分鐘執行一次
      batch-size: 1000
      expired-key-scan-count: 100
    
    # 環境保護配置
    env-guard:
      enabled: true
      dangerous-operations-enabled: false  # 生產環境設為 false
```

### 啟用配置

```java
@SpringBootApplication
@EnableRedisSupport(
    enableSessionManager = true,   // 啟用 Session 管理
    enableCacheManager = true,     // 啟用快取管理
    enableLockManager = true,      // 啟用分散式鎖
    enableCleaner = true,          // 啟用自動清理
    enableEnvGuard = true          // 啟用環境保護
)
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

## 📚 使用方式

詳細的 API 使用說明和最佳實踐請參考 [COMMON_CACHE_USAGE_GUIDE.md](./COMMON_CACHE_USAGE_GUIDE.md) 文件。

### 快速開始範例

#### Session 管理
```java
@Autowired
private SessionManager sessionManager;

// 建立 Session
String sessionId = sessionManager.createSession("user123", userData, 1800);

// 取得 Session 資料
SessionData session = sessionManager.getSession(sessionId);

// 刪除 Session
sessionManager.deleteSession(sessionId);
```

#### 快取管理
```java
@Autowired
private CacheManager cacheManager;

// 設定快取
cacheManager.set("user:123", userData, 3600);

// 取得快取
UserData user = cacheManager.get("user:123", UserData.class);

// 批次操作
Map<String, Object> data = Map.of(
    "user:123", userData1,
    "user:456", userData2
);
cacheManager.setMultiple(data, 1800);
```

#### 分散式鎖
```java
@Autowired
private LockManager lockManager;

// 使用鎖保護執行
String result = lockManager.executeWithLock("critical:section", () -> {
    // 關鍵區段程式碼
    return processData();
}, 10, 30, TimeUnit.SECONDS);
```

## 📦 依賴管理

### Maven 依賴

```xml
<dependency>
    <groupId>com.nanshan</groupId>
    <artifactId>common-cache</artifactId>
    <version>1.0.0-SNAPSHOT</version>
</dependency>
```