# Redisson 共用元件模組 技術規格書

## 1. 架構元件

| 元件名稱                | 類型           | 功能說明                                   |
|------------------------|---------------|--------------------------------------------|
| RedissonConfig         | @Configuration| 建立 RedissonClient                        |
| RedisConnectionManager | Service       | 連線初始化與關閉邏輯                       |
| RedisDataAccessor      | Component     | 提供 Redis Map、Set、List、String 通用操作  |
| SessionManager         | Component     | 儲存/查詢 JWT Session，提供 TTL 控制        |
| CacheManager           | Component     | 快取資料、逾期控管、自動續期策略            |
| LockManager            | Component     | 可重入、公平鎖與讀寫鎖                     |
| RedisCleaner           | Component     | 主動刪除、TTL、定時清理                    |
| RedisKeyHelper         | Utility       | 統一 key 命名規則建構                      |

---

## 2. 主要 Class / Interface 設計

以 RedissonClient、RedisDataAccessor、SessionManager、CacheManager、LockManager 為核心，提供清楚介面定義與範例。

### 2.1 SessionManager（JWT-based）

- Key 設計：`session:{clientId}:{userId}`
- 支援設定 TTL，建議 30 分鐘（可配置）

#### 介面範例
```java
void saveSession(String jwtId, SessionObject session, long ttlSec);
Optional<SessionObject> loadSession(String jwtId);
void deleteSession(String jwtId);
```

#### SessionObject 建議欄位
- userId：使用者唯一識別碼（sub）
- clientId：前端應用來源
- loginTime：登入時間戳記（ISO 格式）
- roles：使用者角色清單
- jwtId：Token 識別碼（jti）
- ipAddress：用戶端 IP（可選）
- deviceInfo：裝置資訊（可選）

---

## 3. Key 命名策略建議

| 類型   | Key Prefix                  | 範例                |
|--------|-----------------------------|---------------------|
| Session| session:{clientId}:{userId} | session:portal:u123 |
| Cache  | cache:{type}:{id}           | cache:product:789   |
| Lock   | lock:{resource}             | lock:order:commit   |

---

## 4. 例外處理設計

- 所有 Redis 操作應封裝 try/catch，回傳 Optional 或拋出自訂例外
- 建議統一例外格式：
  - RedisOperationException
  - SessionNotFoundException

---

## 5. 延伸支援項目（規劃中，暫不開發）

- Redis 延遲任務排隊佇列（基於 SortedSet）
- 使用 Lua Script 封裝原子操作
- 熱 Key 偵測與失效策略
- Multi-tenancy 支援（租戶隔離）

---

## 6. 生產環境限制與防呆設計

- ✅ **已實作**：禁止在生產環境暴露下列操作：
  - ✅ flushdb / flushall（已實作環境保護機制）
  - ✅ Redis KEYS * 查詢（已實作環境保護機制）
  - ✅ 未設定 TTL 的鎖與快取資料（強制設定 TTL）
- ✅ **已實作**：RedisEnvGuard 環境保護機制，根據 spring.profiles.active 禁止高風險操作

### 環境保護機制詳細說明

**保護條件：** `isProductionEnvironment() && !isDangerousOperationsEnabled()`

**預設生產環境 Profile：** `["prod", "production"]`

**被保護的危險操作：**
1. `RedisDataAccessor.keys(String pattern)` - KEYS * 查詢
2. `RedisDataAccessor.flushDatabase()` - FLUSHDB 操作
3. `RedisDataAccessor.flushAllDatabases()` - FLUSHALL 操作
4. `RedisCleaner.deleteByPattern(String pattern)` - 模式刪除操作

**配置方式：**
```yaml
# 開發環境 - 允許危險操作
common:
  cache:
    env-guard:
      enabled: true
      dangerous-operations-enabled: true

# 生產環境 - 禁止危險操作
common:
  cache:
    env-guard:
      enabled: true
      production-profiles:
        - prod
        - production
      dangerous-operations-enabled: false
```

---

## 7. 專案輸出格式

- 元件模組名稱：common-cache
- 提供 @EnableRedisSupport 與 AutoConfiguration

---

## 8. 開發備註

- 使用 Lombok、Slf4j、Spring Context 設計可插拔元件
- 所有元件需 thread-safe
- 保留設定擴充功能點（如快取策略、鎖設定）