package com.nanshan.common.cache.annotation;

import com.nanshan.common.cache.config.CommonCacheAutoConfiguration;
import org.springframework.context.annotation.Import;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 啟用 Redis 支援註解
 * 
 * 使用此註解可以自動配置 Redis 快取、Session 管理、分散式鎖等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(CommonCacheAutoConfiguration.class)
public @interface EnableRedisSupport {
    
    /**
     * 是否啟用 Session 管理
     * 
     * @return 是否啟用 Session 管理
     */
    boolean enableSessionManager() default true;
    
    /**
     * 是否啟用快取管理
     * 
     * @return 是否啟用快取管理
     */
    boolean enableCacheManager() default true;
    
    /**
     * 是否啟用鎖管理
     * 
     * @return 是否啟用鎖管理
     */
    boolean enableLockManager() default true;
    
    /**
     * 是否啟用清理器
     * 
     * @return 是否啟用清理器
     */
    boolean enableCleaner() default true;
    
    /**
     * 是否啟用環境保護
     * 
     * @return 是否啟用環境保護
     */
    boolean enableEnvGuard() default true;
}
