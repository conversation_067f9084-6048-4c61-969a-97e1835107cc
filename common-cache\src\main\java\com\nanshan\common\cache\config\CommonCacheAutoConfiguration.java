package com.nanshan.common.cache.config;

import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.LockManager;
import com.nanshan.common.cache.service.RedisCleaner;
import com.nanshan.common.cache.service.RedisConnectionManager;
import com.nanshan.common.cache.service.RedisDataAccessor;
import com.nanshan.common.cache.service.RedisDiagnosticService;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.common.cache.service.impl.CacheManagerImpl;
import com.nanshan.common.cache.service.impl.LockManagerImpl;
import com.nanshan.common.cache.service.impl.RedisCleanerImpl;
import com.nanshan.common.cache.service.impl.RedisDataAccessorImpl;
import com.nanshan.common.cache.service.impl.SessionManagerImpl;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Common Cache 自動配置類
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@AutoConfiguration
@ConditionalOnClass(RedissonClient.class)
@EnableConfigurationProperties(CommonCacheProperties.class)
@Import({RedissonConfig.class})
@EnableScheduling
public class CommonCacheAutoConfiguration {

    /**
     * Redis 連線管理器
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisConnectionManager redisConnectionManager(RedissonClient redissonClient,
            CommonCacheProperties cacheProperties) {
        log.info("Configuring RedisConnectionManager");
        return new RedisConnectionManager(redissonClient, cacheProperties);
    }

    /**
     * Redis 資料存取器
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisDataAccessor redisDataAccessor(RedissonClient redissonClient,
            CommonCacheProperties cacheProperties,
            org.springframework.core.env.Environment environment) {
        log.info("Configuring RedisDataAccessor");
        return new RedisDataAccessorImpl(redissonClient, cacheProperties, environment);
    }

    /**
     * Session 管理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "common.cache.session", name = "enabled", havingValue = "true", matchIfMissing = true)
    public SessionManager sessionManager(RedisDataAccessor redisDataAccessor,
            CommonCacheProperties cacheProperties) {
        log.info("Configuring SessionManager");
        return new SessionManagerImpl(redisDataAccessor, cacheProperties);
    }

    /**
     * 快取管理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "common.cache.cache", name = "enabled", havingValue = "true", matchIfMissing = true)
    public CacheManager cacheManager(RedisDataAccessor redisDataAccessor,
            CommonCacheProperties cacheProperties) {
        log.info("Configuring CacheManager");
        return new CacheManagerImpl(redisDataAccessor, cacheProperties);
    }

    /**
     * 鎖管理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "common.cache.lock", name = "enabled", havingValue = "true", matchIfMissing = true)
    public LockManager lockManager(RedissonClient redissonClient,
            CommonCacheProperties cacheProperties) {
        log.info("Configuring LockManager");
        return new LockManagerImpl(redissonClient, cacheProperties);
    }

    /**
     * Redis 清理器
     */
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnProperty(prefix = "common.cache.cleaner", name = "enabled", havingValue = "true", matchIfMissing = true)
    public RedisCleaner redisCleaner(RedisDataAccessor redisDataAccessor,
            SessionManager sessionManager,
            CacheManager cacheManager,
            CommonCacheProperties cacheProperties,
            Environment environment) {
        log.info("Configuring RedisCleaner");
        return new RedisCleanerImpl(redisDataAccessor, sessionManager, cacheManager, cacheProperties, environment);
    }

    /**
     * Redis 診斷服務
     */
    @Bean
    @ConditionalOnMissingBean
    public RedisDiagnosticService redisDiagnosticService(RedissonClient redissonClient) {
        log.info("Configuring RedisDiagnosticService");
        return new RedisDiagnosticService(redissonClient);
    }
}
