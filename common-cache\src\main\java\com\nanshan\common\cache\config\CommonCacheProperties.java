package com.nanshan.common.cache.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.List;

/**
 * Common Cache 配置屬性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@ConfigurationProperties(prefix = "common.cache")
public class CommonCacheProperties {

    /**
     * Redis 連線配置
     */
    private Redis redis = new Redis();

    /**
     * 環境保護設定
     */
    private EnvGuard envGuard = new EnvGuard();

    /**
     * Session 管理配置
     */
    private Session session = new Session();

    /**
     * 快取管理配置
     */
    private Cache cache = new Cache();

    /**
     * 鎖管理配置
     */
    private Lock lock = new Lock();

    /**
     * 清理器配置
     */
    private Cleaner cleaner = new Cleaner();

    @Data
    public static class EnvGuard {

        /**
         * 是否啟用環境保護
         */
        private boolean enabled = true;

        /**
         * 生產環境 Profile 清單
         */
        private List<String> productionProfiles = List.of("prod", "production");

        /**
         * 是否允許危險操作
         */
        private boolean dangerousOperationsEnabled = false;
    }

    @Data
    public static class Session {

        /**
         * 預設存活時間（秒）
         */
        private long defaultTimeToLive = 1800; // 30 分鐘

        /**
         * Key 前綴
         */
        private String keyPrefix = "session";

        /**
         * 清理間隔 (秒)
         */
        private long cleanupInterval = 300; // 5 分鐘

        /**
         * 是否啟用自動續期
         */
        private boolean autoRenewal = true;

        /**
         * 續期閾值 (剩餘 TTL 比例)
         */
        private double renewalThreshold = 0.2; // 20%
    }

    @Data
    public static class Cache {

        /**
         * 預設存活時間（秒）
         */
        private long defaultTimeToLive = 3600; // 1 小時

        /**
         * Key 前綴
         */
        private String keyPrefix = "cache";

        /**
         * 是否啟用自動續期
         */
        private boolean autoRenewal = true;

        /**
         * 續期閾值 (剩餘 TTL 比例)
         */
        private double renewalThreshold = 0.2; // 20%

        /**
         * 最大快取大小
         */
        private long maxSize = 10000;
    }

    @Data
    public static class Lock {

        /**
         * 預設租約時間 (秒)
         */
        private long defaultLeaseTime = 30;

        /**
         * 預設等待時間 (秒)
         */
        private long defaultWaitTime = 10;

        /**
         * Key 前綴
         */
        private String keyPrefix = "lock";

        /**
         * 是否啟用公平鎖
         */
        private boolean fairLockEnabled = true;

        /**
         * 是否啟用看門狗機制
         */
        private boolean watchdogEnabled = true;
    }

    @Data
    public static class Cleaner {

        /**
         * 是否啟用清理器
         */
        private boolean enabled = true;

        /**
         * 排程間隔 (秒)
         */
        private long scheduleInterval = 600; // 10 分鐘

        /**
         * 批次大小
         */
        private int batchSize = 1000;

        /**
         * 過期 Key 掃描數量
         */
        private int expiredKeyScanCount = 100;

        /**
         * 是否啟用統計
         */
        private boolean statisticsEnabled = true;
    }

    /**
     * Redis 連線配置類
     */
    @Data
    public static class Redis {

        /**
         * 連線池配置
         */
        private ConnectionPool connectionPool = new ConnectionPool();

        /**
         * 超時配置
         */
        private Timeout timeout = new Timeout();

        /**
         * 重試配置
         */
        private Retry retry = new Retry();

        /**
         * 執行緒池配置
         */
        private ThreadPool threadPool = new ThreadPool();

        /**
         * 其他配置
         */
        private Misc misc = new Misc();

        /**
         * 連線池配置類
         */
        @Data
        public static class ConnectionPool {

            /**
             * 連線池大小
             */
            private int poolSize = 64;

            /**
             * 最小空閒連線數
             */
            private int minimumIdleSize = 10;

            /**
             * 空閒連線超時時間 (毫秒)
             */
            private int idleConnectionTimeout = 10000;
        }

        /**
         * 超時配置類
         */
        @Data
        public static class Timeout {

            /**
             * 連線超時時間 (毫秒)
             */
            private int connectTimeout = 10000;

            /**
             * 命令執行超時時間 (毫秒)
             */
            private int commandTimeout = 3000;
        }

        /**
         * 重試配置類
         */
        @Data
        public static class Retry {

            /**
             * 重試次數
             */
            private int attempts = 3;

            /**
             * 重試間隔 (毫秒)
             */
            private int interval = 1500;
        }

        /**
         * 執行緒池配置類
         */
        @Data
        public static class ThreadPool {

            /**
             * 執行緒數
             */
            private int threads = 16;

            /**
             * Netty 執行緒數
             */
            private int nettyThreads = 32;
        }

        /**
         * 其他配置類
         */
        @Data
        public static class Misc {

            /**
             * 是否啟用 Keep Alive
             */
            private boolean keepAlive = true;

            /**
             * 是否啟用 TCP No Delay
             */
            private boolean tcpNoDelay = true;
        }
    }
}
