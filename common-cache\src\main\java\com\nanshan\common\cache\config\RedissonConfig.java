package com.nanshan.common.cache.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.TimeZone;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson 配置類
 *
 * 建立和配置 RedissonClient Bean
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@EnableConfigurationProperties({RedisProperties.class})
public class RedissonConfig {

    private final RedisProperties redisProperties;

    /**
     * 建立 RedissonClient Bean
     *
     * @return RedissonClient 實例
     */
    @Bean
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 配置單機模式
        configureSingleServer(config);

        // 配置強化的 Jackson 編解碼器
        ObjectMapper objectMapper = createRobustObjectMapper();
        config.setCodec(new JsonJacksonCodec(objectMapper));

        System.out.println("Redisson client configured successfully with enhanced JSON parsing");
        return Redisson.create(config);
    }

    /**
     * 創建強化的 ObjectMapper，能夠處理控制字符
     *
     * @return 配置完善的 ObjectMapper
     */
    private ObjectMapper createRobustObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 支援 Java 8 時間 API
        objectMapper.registerModule(new JavaTimeModule());

        // 配置 JSON 解析特性以處理控制字符 - 這是解決問題的關鍵
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

        // 配置反序列化特性以提高容錯性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 配置序列化特性
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // 設置時區
        objectMapper.setTimeZone(TimeZone.getDefault());

        System.out.println("Created robust ObjectMapper with control character handling");
        return objectMapper;
    }

    /**
     * 配置單機 Redis 伺服器
     *
     * @param config Redisson 配置
     */
    private void configureSingleServer(Config config) {
        String address = String.format("redis://%s:%d",
                redisProperties.getHost(), redisProperties.getPort());

        var singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setConnectionPoolSize(10)
                .setConnectionMinimumIdleSize(2)
                .setConnectTimeout(3000)
                .setTimeout(3000)
                // PubSub 相關配置 - 解決分散式鎖問題
                .setSubscriptionConnectionPoolSize(5) // PubSub 連接池大小
                .setSubscriptionsPerConnection(5) // 每個連接的訂閱數
                .setSubscriptionTimeout(15000) // PubSub 訂閱超時（15秒）
                // 重試和容錯配置
                .setRetryAttempts(3) // 重試次數
                .setRetryInterval(1500) // 重試間隔（毫秒）

                // 保持連接活躍
                .setKeepAlive(true) // 啟用 TCP Keep-Alive
                .setTcpNoDelay(true);                          // 啟用 TCP No Delay

        // 只有在密碼不為空時才設置密碼
        if (redisProperties.getPassword() != null && !redisProperties.getPassword().trim().isEmpty()) {
            singleServerConfig.setPassword(redisProperties.getPassword());
        }

        if (redisProperties.getUsername() != null && !redisProperties.getUsername().trim().isEmpty()) {
            singleServerConfig.setUsername(redisProperties.getUsername());
        }

        System.out.println("Configured Redisson single server with PubSub support: " + address);
    }
}
