package com.nanshan.common.cache.exception;

/**
 * 快取操作例外
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class CacheOperationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String cacheKey;
    private final String operation;
    
    public CacheOperationException(String message) {
        super(message);
        this.cacheKey = null;
        this.operation = null;
    }
    
    public CacheOperationException(String cacheKey, String operation, String message) {
        super(String.format("Cache operation '%s' failed for key '%s': %s", operation, cacheKey, message));
        this.cacheKey = cacheKey;
        this.operation = operation;
    }
    
    public CacheOperationException(String cacheKey, String operation, String message, Throwable cause) {
        super(String.format("Cache operation '%s' failed for key '%s': %s", operation, cacheKey, message), cause);
        this.cacheKey = cacheKey;
        this.operation = operation;
    }
    
    public String getCacheKey() {
        return cacheKey;
    }
    
    public String getOperation() {
        return operation;
    }
}
