package com.nanshan.common.cache.exception;

/**
 * 鎖操作例外
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class LockOperationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String lockKey;
    private final String operation;
    
    public LockOperationException(String message) {
        super(message);
        this.lockKey = null;
        this.operation = null;
    }
    
    public LockOperationException(String lockKey, String operation, String message) {
        super(String.format("Lock operation '%s' failed for key '%s': %s", operation, lockKey, message));
        this.lockKey = lockKey;
        this.operation = operation;
    }
    
    public LockOperationException(String lockKey, String operation, String message, Throwable cause) {
        super(String.format("Lock operation '%s' failed for key '%s': %s", operation, lockKey, message), cause);
        this.lockKey = lockKey;
        this.operation = operation;
    }
    
    public String getLockKey() {
        return lockKey;
    }
    
    public String getOperation() {
        return operation;
    }
}
