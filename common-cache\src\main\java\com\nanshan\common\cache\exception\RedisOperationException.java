package com.nanshan.common.cache.exception;

/**
 * Redis 操作例外
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class RedisOperationException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String operation;
    private final String key;
    
    public RedisOperationException(String message) {
        super(message);
        this.operation = null;
        this.key = null;
    }
    
    public RedisOperationException(String message, Throwable cause) {
        super(message, cause);
        this.operation = null;
        this.key = null;
    }
    
    public RedisOperationException(String operation, String key, String message) {
        super(String.format("Redis operation '%s' failed for key '%s': %s", operation, key, message));
        this.operation = operation;
        this.key = key;
    }
    
    public RedisOperationException(String operation, String key, String message, Throwable cause) {
        super(String.format("Redis operation '%s' failed for key '%s': %s", operation, key, message), cause);
        this.operation = operation;
        this.key = key;
    }
    
    public String getOperation() {
        return operation;
    }
    
    public String getKey() {
        return key;
    }
}
