package com.nanshan.common.cache.exception;

/**
 * Session 未找到例外
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SessionNotFoundException extends RuntimeException {
    
    private static final long serialVersionUID = 1L;
    
    private final String sessionId;
    
    public SessionNotFoundException(String sessionId) {
        super(String.format("Session not found: %s", sessionId));
        this.sessionId = sessionId;
    }
    
    public SessionNotFoundException(String sessionId, String message) {
        super(String.format("Session not found: %s - %s", sessionId, message));
        this.sessionId = sessionId;
    }
    
    public SessionNotFoundException(String sessionId, Throwable cause) {
        super(String.format("Session not found: %s", sessionId), cause);
        this.sessionId = sessionId;
    }
    
    public String getSessionId() {
        return sessionId;
    }
}
