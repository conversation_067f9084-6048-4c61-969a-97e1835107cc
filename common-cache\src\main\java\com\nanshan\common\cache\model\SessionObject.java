package com.nanshan.common.cache.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * JWT Session 資料模型
 * 
 * 用於儲存使用者 Session 資訊，支援 JWT Token 管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SessionObject implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 使用者唯一識別碼 (JWT sub)
     */
    private String userId;
    
    /**
     * 前端應用來源
     */
    private String clientId;
    
    /**
     * Token 識別碼 (JWT jti)
     */
    private String jwtId;
    
    /**
     * 登入時間戳記
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime loginTime;
    
    /**
     * Token 過期時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expireTime;
    
    /**
     * 使用者角色清單
     */
    private List<String> roles;
    
    /**
     * 使用者權限清單
     */
    private List<String> permissions;
    
    /**
     * 用戶端 IP 位址
     */
    private String ipAddress;
    
    /**
     * 裝置資訊
     */
    private String deviceInfo;
    
    /**
     * 使用者代理 (User-Agent)
     */
    private String userAgent;
    
    /**
     * Session 建立時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * Session 最後更新時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 最後活動時間
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastActiveTime;
    
    /**
     * 擴展屬性 (用於儲存額外的自訂資訊)
     */
    private Map<String, Object> attributes;
    
    /**
     * Session 狀態
     */
    private SessionStatus status;
    
    /**
     * 檢查 Session 是否已過期
     * 
     * @return 是否已過期
     */
    public boolean isExpired() {
        if (expireTime == null) {
            return false;
        }
        return LocalDateTime.now().isAfter(expireTime);
    }
    
    /**
     * 檢查 Session 是否為活躍狀態
     * 
     * @return 是否為活躍狀態
     */
    public boolean isActive() {
        return status == SessionStatus.ACTIVE && !isExpired();
    }
    
    /**
     * 更新最後活動時間
     */
    public void updateLastActiveTime() {
        this.lastActiveTime = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 設定 Session 為過期狀態
     */
    public void expire() {
        this.status = SessionStatus.EXPIRED;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 設定 Session 為登出狀態
     */
    public void logout() {
        this.status = SessionStatus.LOGGED_OUT;
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * Session 狀態列舉
     */
    public enum SessionStatus {
        /**
         * 活躍狀態
         */
        ACTIVE,
        
        /**
         * 已過期
         */
        EXPIRED,
        
        /**
         * 已登出
         */
        LOGGED_OUT,
        
        /**
         * 已鎖定
         */
        LOCKED
    }
}
