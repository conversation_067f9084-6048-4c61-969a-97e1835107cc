package com.nanshan.common.cache.model;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SessionObject 建構器工具類
 * 
 * 提供便利的方法來建立 SessionObject 實例
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SessionObjectBuilder {
    
    /**
     * 建立基本的 Session 物件
     * 
     * @param userId 使用者 ID
     * @param clientId 客戶端 ID
     * @param jwtId JWT ID
     * @return SessionObject
     */
    public static SessionObject createBasicSession(String userId, String clientId, String jwtId) {
        LocalDateTime now = LocalDateTime.now();
        
        return SessionObject.builder()
                .userId(userId)
                .clientId(clientId)
                .jwtId(jwtId)
                .loginTime(now)
                .createdAt(now)
                .updatedAt(now)
                .lastActiveTime(now)
                .status(SessionObject.SessionStatus.ACTIVE)
                .attributes(new HashMap<>())
                .build();
    }
    
    /**
     * 建立完整的 Session 物件
     * 
     * @param userId 使用者 ID
     * @param clientId 客戶端 ID
     * @param jwtId JWT ID
     * @param roles 角色清單
     * @param permissions 權限清單
     * @param ipAddress IP 位址
     * @param deviceInfo 裝置資訊
     * @param userAgent 使用者代理
     * @param ttlSeconds TTL 秒數
     * @return SessionObject
     */
    public static SessionObject createFullSession(String userId, String clientId, String jwtId,
                                                List<String> roles, List<String> permissions,
                                                String ipAddress, String deviceInfo, String userAgent,
                                                long ttlSeconds) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expireTime = now.plusSeconds(ttlSeconds);
        
        return SessionObject.builder()
                .userId(userId)
                .clientId(clientId)
                .jwtId(jwtId)
                .loginTime(now)
                .expireTime(expireTime)
                .roles(roles)
                .permissions(permissions)
                .ipAddress(ipAddress)
                .deviceInfo(deviceInfo)
                .userAgent(userAgent)
                .createdAt(now)
                .updatedAt(now)
                .lastActiveTime(now)
                .status(SessionObject.SessionStatus.ACTIVE)
                .attributes(new HashMap<>())
                .build();
    }
    
    /**
     * 從現有 Session 建立副本
     * 
     * @param original 原始 Session
     * @return 新的 SessionObject
     */
    public static SessionObject copyFrom(SessionObject original) {
        if (original == null) {
            return null;
        }
        
        return SessionObject.builder()
                .userId(original.getUserId())
                .clientId(original.getClientId())
                .jwtId(original.getJwtId())
                .loginTime(original.getLoginTime())
                .expireTime(original.getExpireTime())
                .roles(original.getRoles())
                .permissions(original.getPermissions())
                .ipAddress(original.getIpAddress())
                .deviceInfo(original.getDeviceInfo())
                .userAgent(original.getUserAgent())
                .createdAt(original.getCreatedAt())
                .updatedAt(LocalDateTime.now())
                .lastActiveTime(original.getLastActiveTime())
                .status(original.getStatus())
                .attributes(original.getAttributes() != null ? 
                    new HashMap<>(original.getAttributes()) : new HashMap<>())
                .build();
    }
    
    /**
     * 更新 Session 的過期時間
     * 
     * @param session 要更新的 Session
     * @param ttlSeconds 新的 TTL 秒數
     * @return 更新後的 SessionObject
     */
    public static SessionObject updateExpireTime(SessionObject session, long ttlSeconds) {
        if (session == null) {
            return null;
        }
        
        SessionObject updated = copyFrom(session);
        updated.setExpireTime(LocalDateTime.now().plusSeconds(ttlSeconds));
        updated.setUpdatedAt(LocalDateTime.now());
        return updated;
    }
}
