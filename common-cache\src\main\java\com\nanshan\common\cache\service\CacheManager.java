package com.nanshan.common.cache.service;

import java.time.Duration;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 快取管理服務介面
 * 
 * 提供分散式快取的基本操作，包括儲存、查詢、刪除、TTL 管理等功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface CacheManager {
    
    // ==================== 基本快取操作 ====================
    
    /**
     * 儲存快取資料（使用預設 TTL）
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param value 快取值
     * @param <T> 值類型
     */
    <T> void put(String type, String id, T value);
    
    /**
     * 儲存快取資料（指定 TTL）
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param value 快取值
     * @param ttl TTL 時間
     * @param timeUnit 時間單位
     * @param <T> 值類型
     */
    <T> void put(String type, String id, T value, long ttl, TimeUnit timeUnit);
    
    /**
     * 儲存快取資料（使用 Duration）
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param value 快取值
     * @param duration 持續時間
     * @param <T> 值類型
     */
    <T> void put(String type, String id, T value, Duration duration);
    
    /**
     * 獲取快取資料
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 快取值（Optional）
     */
    <T> Optional<T> get(String type, String id, Class<T> clazz);
    
    /**
     * 檢查快取是否存在
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @return 是否存在
     */
    boolean exists(String type, String id);
    
    /**
     * 刪除快取資料
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @return 是否刪除成功
     */
    boolean remove(String type, String id);
    
    /**
     * 刪除指定類型的所有快取
     * 
     * @param type 快取類型
     * @return 刪除的快取數量
     */
    long removeAll(String type);
    
    // ==================== TTL 管理 ====================
    
    /**
     * 獲取快取的剩餘存活時間
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param timeUnit 時間單位
     * @return 剩餘存活時間，-1 表示永不過期，-2 表示不存在
     */
    long getRemainingTimeToLive(String type, String id, TimeUnit timeUnit);
    
    /**
     * 設定快取的 TTL
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param ttl TTL 時間
     * @param timeUnit 時間單位
     * @return 是否設定成功
     */
    boolean setTimeToLive(String type, String id, long ttl, TimeUnit timeUnit);
    
    /**
     * 續期快取（使用預設 TTL）
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @return 是否續期成功
     */
    boolean renew(String type, String id);
    
    /**
     * 續期快取（指定 TTL）
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @param ttl TTL 時間
     * @param timeUnit 時間單位
     * @return 是否續期成功
     */
    boolean renew(String type, String id, long ttl, TimeUnit timeUnit);
    
    // ==================== 批量操作 ====================
    
    /**
     * 批量儲存快取資料（使用預設 TTL）
     * 
     * @param type 快取類型
     * @param data 快取資料 Map
     * @param <T> 值類型
     */
    <T> void multiPut(String type, Map<String, T> data);
    
    /**
     * 批量儲存快取資料（指定 TTL）
     * 
     * @param type 快取類型
     * @param data 快取資料 Map
     * @param ttl TTL 時間
     * @param timeUnit 時間單位
     * @param <T> 值類型
     */
    <T> void multiPut(String type, Map<String, T> data, long ttl, TimeUnit timeUnit);
    
    /**
     * 批量獲取快取資料
     * 
     * @param type 快取類型
     * @param ids 快取 ID 集合
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 快取資料 Map
     */
    <T> Map<String, T> multiGet(String type, Collection<String> ids, Class<T> clazz);
    
    /**
     * 批量刪除快取資料
     * 
     * @param type 快取類型
     * @param ids 快取 ID 集合
     * @return 刪除的快取數量
     */
    long multiRemove(String type, Collection<String> ids);
    
    // ==================== 查詢和統計 ====================
    
    /**
     * 獲取指定類型的所有快取 ID
     * 
     * @param type 快取類型
     * @return 快取 ID 集合
     * @apiNote 此方法在生產環境中可能有性能問題，建議謹慎使用
     */
    Set<String> getAllCacheIds(String type);
    
    /**
     * 獲取快取統計資訊
     * 
     * @return 快取統計資訊
     */
    CacheStats getStats();
    
    /**
     * 清理過期的快取
     * 
     * @return 清理的快取數量
     */
    long cleanupExpired();
    
    /**
     * 快取統計資訊介面
     */
    interface CacheStats {
        long getHitCount();
        long getMissCount();
        double getHitRate();
        long getTotalRequests();
    }
}
