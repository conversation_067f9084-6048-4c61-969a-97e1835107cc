package com.nanshan.common.cache.service;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 分散式鎖管理介面
 *
 * 提供完整的分散式鎖功能，包括： - 可重入鎖（阻塞式、非阻塞式、可中斷式） - 公平鎖 - 讀寫鎖 - 自旋鎖 - 圍欄鎖 - 多重鎖 - 信號量 -
 * 倒數計時器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface LockManager {

    // ==================== 可重入鎖 ====================
    /**
     * 阻塞式獲取鎖，會一直等待直到獲取成功
     *
     * @param resource 資源名稱
     */
    void lock(String resource);

    /**
     * 阻塞式獲取鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     */
    void lock(String resource, long leaseTime, TimeUnit timeUnit);

    /**
     * 可中斷的阻塞式獲取鎖
     *
     * @param resource 資源名稱
     * @throws InterruptedException 如果線程被中斷
     */
    void lockInterruptibly(String resource) throws InterruptedException;

    /**
     * 可中斷的阻塞式獲取鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @throws InterruptedException 如果線程被中斷
     */
    void lockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException;

    /**
     * 非阻塞式嘗試獲取鎖，立即返回結果
     *
     * @param resource 資源名稱
     * @return 是否獲取成功
     */
    boolean tryLock(String resource);

    /**
     * 非阻塞式嘗試獲取鎖，並指定等待時間和租約時間
     *
     * @param resource 資源名稱
     * @param waitTime 等待時間
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @return 是否獲取成功
     */
    boolean tryLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit);

    /**
     * 非阻塞式嘗試獲取鎖，並指定等待時間和租約時間
     *
     * @param resource 資源名稱
     * @param waitDuration 等待時間
     * @param leaseDuration 租約時間
     * @return 是否獲取成功
     */
    boolean tryLock(String resource, Duration waitDuration, Duration leaseDuration);

    /**
     * 釋放資源上的所有類型鎖（由當前線程持有的）
     *
     * @param resource 資源名稱
     * @return 是否至少釋放了一個鎖
     */
    boolean unlock(String resource);

    /**
     * 釋放特定類型的鎖
     *
     * @param resource 資源名稱
     * @param lockType 鎖類型
     * @return 是否釋放成功
     */
    boolean unlock(String resource, LockType lockType);

    /**
     * 強制釋放資源上的所有類型鎖
     *
     * @param resource 資源名稱
     * @return 是否至少釋放了一個鎖
     */
    boolean forceUnlock(String resource);

    /**
     * 強制釋放特定類型的鎖
     *
     * @param resource 資源名稱
     * @param lockType 鎖類型
     * @return 是否釋放成功
     */
    boolean forceUnlock(String resource, LockType lockType);

    /**
     * 檢查資源是否被任何類型的鎖鎖定
     *
     * @param resource 資源名稱
     * @return 是否被鎖定
     */
    boolean isLocked(String resource);

    /**
     * 檢查資源是否被特定類型的鎖鎖定
     *
     * @param resource 資源名稱
     * @param lockType 鎖類型
     * @return 是否被鎖定
     */
    boolean isLocked(String resource, LockType lockType);

    /**
     * 檢查鎖是否被當前執行緒持有
     *
     * @param resource 資源名稱
     * @return 是否被當前執行緒持有
     */
    boolean isHeldByCurrentThread(String resource);

    /**
     * 檢查特定類型的鎖是否被當前執行緒持有
     *
     * @param resource 資源名稱
     * @param lockType 鎖類型
     * @return 是否被當前執行緒持有
     */
    boolean isHeldByCurrentThread(String resource, LockType lockType);

    /**
     * 獲取鎖的剩餘租約時間
     *
     * @param resource 資源名稱
     * @param timeUnit 時間單位
     * @return 剩餘租約時間，-1 表示永不過期，-2 表示不存在
     */
    long getRemainingLeaseTime(String resource, TimeUnit timeUnit);

    /**
     * 鎖類型枚舉
     */
    enum LockType {
        /**
         * 普通可重入鎖
         */
        REENTRANT,
        /**
         * 公平鎖
         */
        FAIR,
        /**
         * 讀鎖
         */
        READ,
        /**
         * 寫鎖
         */
        WRITE
    }

    // ==================== 公平鎖 ====================
    /**
     * 阻塞式獲取公平鎖
     *
     * @param resource 資源名稱
     */
    void fairLock(String resource);

    /**
     * 阻塞式獲取公平鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     */
    void fairLock(String resource, long leaseTime, TimeUnit timeUnit);

    /**
     * 可中斷的阻塞式獲取公平鎖
     *
     * @param resource 資源名稱
     * @throws InterruptedException 如果線程被中斷
     */
    void fairLockInterruptibly(String resource) throws InterruptedException;

    /**
     * 可中斷的阻塞式獲取公平鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @throws InterruptedException 如果線程被中斷
     */
    void fairLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException;

    /**
     * 非阻塞式嘗試獲取公平鎖
     *
     * @param resource 資源名稱
     * @return 是否獲取成功
     */
    boolean tryFairLock(String resource);

    /**
     * 非阻塞式嘗試獲取公平鎖，並指定等待時間和租約時間
     *
     * @param resource 資源名稱
     * @param waitTime 等待時間
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @return 是否獲取成功
     */
    boolean tryFairLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit);

    // ==================== 讀寫鎖 ====================
    /**
     * 阻塞式獲取讀鎖
     *
     * @param resource 資源名稱
     */
    void readLock(String resource);

    /**
     * 阻塞式獲取讀鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     */
    void readLock(String resource, long leaseTime, TimeUnit timeUnit);

    /**
     * 可中斷的阻塞式獲取讀鎖
     *
     * @param resource 資源名稱
     * @throws InterruptedException 如果線程被中斷
     */
    void readLockInterruptibly(String resource) throws InterruptedException;

    /**
     * 可中斷的阻塞式獲取讀鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @throws InterruptedException 如果線程被中斷
     */
    void readLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException;

    /**
     * 非阻塞式嘗試獲取讀鎖
     *
     * @param resource 資源名稱
     * @return 是否獲取成功
     */
    boolean tryReadLock(String resource);

    /**
     * 非阻塞式嘗試獲取讀鎖，並指定等待時間和租約時間
     *
     * @param resource 資源名稱
     * @param waitTime 等待時間
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @return 是否獲取成功
     */
    boolean tryReadLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit);

    /**
     * 阻塞式獲取寫鎖
     *
     * @param resource 資源名稱
     */
    void writeLock(String resource);

    /**
     * 阻塞式獲取寫鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     */
    void writeLock(String resource, long leaseTime, TimeUnit timeUnit);

    /**
     * 可中斷的阻塞式獲取寫鎖
     *
     * @param resource 資源名稱
     * @throws InterruptedException 如果線程被中斷
     */
    void writeLockInterruptibly(String resource) throws InterruptedException;

    /**
     * 可中斷的阻塞式獲取寫鎖，並指定租約時間
     *
     * @param resource 資源名稱
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @throws InterruptedException 如果線程被中斷
     */
    void writeLockInterruptibly(String resource, long leaseTime, TimeUnit timeUnit) throws InterruptedException;

    /**
     * 非阻塞式嘗試獲取寫鎖
     *
     * @param resource 資源名稱
     * @return 是否獲取成功
     */
    boolean tryWriteLock(String resource);

    /**
     * 非阻塞式嘗試獲取寫鎖，並指定等待時間和租約時間
     *
     * @param resource 資源名稱
     * @param waitTime 等待時間
     * @param leaseTime 租約時間
     * @param timeUnit 時間單位
     * @return 是否獲取成功
     */
    boolean tryWriteLock(String resource, long waitTime, long leaseTime, TimeUnit timeUnit);

}
