package com.nanshan.common.cache.service;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * Redis 清理服務介面
 * 
 * 提供主動刪除、TTL 設定、定時清理功能
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RedisCleaner {
    
    /**
     * 清理過期的 Session
     * 
     * @return 清理的 Session 數量
     */
    long cleanupExpiredSessions();
    
    /**
     * 清理過期的快取
     * 
     * @return 清理的快取數量
     */
    long cleanupExpiredCaches();
    
    /**
     * 清理過期的鎖
     * 
     * @return 清理的鎖數量
     */
    long cleanupExpiredLocks();
    
    /**
     * 清理所有過期的 Key
     * 
     * @return 清理的 Key 總數
     */
    long cleanupAllExpired();
    
    /**
     * 根據模式刪除 Key
     * 
     * @param pattern Key 模式
     * @return 刪除的 Key 數量
     */
    long deleteByPattern(String pattern);
    
    /**
     * 批量刪除 Key
     * 
     * @param keys Key 集合
     * @return 刪除的 Key 數量
     */
    long deleteBatch(Set<String> keys);
    
    /**
     * 設定 Key 的存活時間
     * 
     * @param key Key
     * @param timeToLiveSeconds 存活時間（秒）
     * @return 是否設定成功
     */
    boolean setTimeToLive(String key, long timeToLiveSeconds);
    
    /**
     * 批量設定 Key 的存活時間
     * 
     * @param keys Key 集合
     * @param timeToLiveSeconds 存活時間（秒）
     * @return 設定成功的 Key 數量
     */
    long setBatchTimeToLive(Set<String> keys, long timeToLiveSeconds);
    
    /**
     * 移除 Key 的存活時間限制（設為永不過期）
     * 
     * @param key Key
     * @return 是否移除成功
     */
    boolean removeTimeToLive(String key);
    
    /**
     * 獲取清理統計資訊
     * 
     * @return 清理統計資訊
     */
    CleanupStats getCleanupStats();
    
    /**
     * 重置清理統計資訊
     */
    void resetCleanupStats();
    
    /**
     * 執行手動清理
     * 
     * @return 清理結果
     */
    CleanupResult performManualCleanup();
    
    /**
     * 清理統計資訊介面
     */
    interface CleanupStats {
        
        /**
         * 獲取總清理次數
         * 
         * @return 總清理次數
         */
        long getTotalCleanups();
        
        /**
         * 獲取清理的 Session 總數
         * 
         * @return 清理的 Session 總數
         */
        long getTotalSessionsCleaned();
        
        /**
         * 獲取清理的快取總數
         * 
         * @return 清理的快取總數
         */
        long getTotalCachesCleaned();
        
        /**
         * 獲取清理的鎖總數
         * 
         * @return 清理的鎖總數
         */
        long getTotalLocksCleaned();
        
        /**
         * 獲取最後清理時間
         * 
         * @return 最後清理時間
         */
        LocalDateTime getLastCleanupTime();
        
        /**
         * 獲取平均清理耗時
         * 
         * @return 平均清理耗時（毫秒）
         */
        double getAverageCleanupDuration();
    }
    
    /**
     * 清理結果介面
     */
    interface CleanupResult {
        
        /**
         * 獲取清理的 Session 數量
         * 
         * @return 清理的 Session 數量
         */
        long getSessionsCleaned();
        
        /**
         * 獲取清理的快取數量
         * 
         * @return 清理的快取數量
         */
        long getCachesCleaned();
        
        /**
         * 獲取清理的鎖數量
         * 
         * @return 清理的鎖數量
         */
        long getLocksCleaned();
        
        /**
         * 獲取清理耗時
         * 
         * @return 清理耗時（毫秒）
         */
        long getDurationMs();
        
        /**
         * 是否清理成功
         * 
         * @return 是否清理成功
         */
        boolean isSuccess();
        
        /**
         * 獲取錯誤訊息
         * 
         * @return 錯誤訊息
         */
        String getErrorMessage();
    }
}
