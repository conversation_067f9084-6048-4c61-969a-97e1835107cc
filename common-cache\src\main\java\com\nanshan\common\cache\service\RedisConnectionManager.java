package com.nanshan.common.cache.service;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.exception.RedisOperationException;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;



/**
 * Redis 連線管理服務
 *
 * 負責 Redis 連線的初始化、健康檢查和關閉邏輯
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class RedisConnectionManager implements InitializingBean, DisposableBean {

    private static final Logger log = LoggerFactory.getLogger(RedisConnectionManager.class);

    private final RedissonClient redissonClient;
    private final CommonCacheProperties cacheProperties;

    private volatile boolean initialized = false;
    private volatile boolean healthy = false;

    public RedisConnectionManager(RedissonClient redissonClient, CommonCacheProperties cacheProperties) {
        this.redissonClient = redissonClient;
        this.cacheProperties = cacheProperties;
    }
    
    /**
     * 初始化連線
     */
    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            initializeConnection();
            this.initialized = true;
            log.info("Redis connection manager initialized successfully");
        } catch (Exception e) {
            log.error("Failed to initialize Redis connection manager", e);
            throw new RedisOperationException("Failed to initialize Redis connection", e);
        }
    }
    
    /**
     * 關閉連線
     */
    @Override
    public void destroy() throws Exception {
        try {
            closeConnection();
            log.info("Redis connection manager destroyed successfully");
        } catch (Exception e) {
            log.error("Error occurred while destroying Redis connection manager", e);
        }
    }
    
    /**
     * 檢查連線是否健康
     * 
     * @return 連線是否健康
     */
    public boolean isHealthy() {
        if (!initialized) {
            return false;
        }
        
        try {
            // 執行 ping 命令檢查連線
            redissonClient.getNodesGroup().pingAll();
            this.healthy = true;
            return this.healthy;
        } catch (Exception e) {
            log.warn("Redis health check failed", e);
            this.healthy = false;
            return false;
        }
    }
    
    /**
     * 檢查連線是否已初始化
     * 
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 獲取連線資訊
     * 
     * @return 連線資訊字串
     */
    public String getConnectionInfo() {
        if (!initialized) {
            return "Not initialized";
        }
        
        try {
            // 簡化實現，避免使用 protected 方法
            return "Redis connection active";
        } catch (Exception e) {
            log.warn("Failed to get connection info", e);
            return "Error getting connection info";
        }
    }
    
    /**
     * 重新連線
     * 
     * @throws RedisOperationException 重新連線失敗時拋出
     */
    public void reconnect() throws RedisOperationException {
        try {
            log.info("Attempting to reconnect to Redis...");
            
            // 檢查當前連線狀態
            if (isHealthy()) {
                log.info("Redis connection is already healthy, no need to reconnect");
                return;
            }
            
            // 重新初始化連線
            initializeConnection();
            
            log.info("Redis reconnection successful");
        } catch (Exception e) {
            log.error("Failed to reconnect to Redis", e);
            throw new RedisOperationException("Failed to reconnect to Redis", e);
        }
    }
    
    /**
     * 獲取連線統計資訊
     * 
     * @return 統計資訊
     */
    public ConnectionStats getConnectionStats() {
        if (!initialized) {
            return new ConnectionStats(false, false, null, null);
        }

        try {
            return new ConnectionStats(true, isHealthy(), getConnectionInfo(), null);
        } catch (Exception e) {
            log.warn("Failed to get connection stats", e);
            return new ConnectionStats(true, false, null, e.getMessage());
        }
    }
    
    /**
     * 初始化連線
     */
    private void initializeConnection() {
        // 檢查 RedissonClient 是否可用
        if (redissonClient == null) {
            throw new IllegalStateException("RedissonClient is not available");
        }
        
        // 執行連線測試
        try {
            redissonClient.getNodesGroup().pingAll();
            this.healthy = true;
            log.info("Redis connection test successful");
        } catch (Exception e) {
            this.healthy = false;
            throw new RedisOperationException("Redis connection test failed", e);
        }
        
        // 設定關閉鉤子
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                closeConnection();
            } catch (Exception e) {
                log.error("Error in shutdown hook", e);
            }
        }));
    }
    
    /**
     * 關閉連線
     */
    private void closeConnection() {
        if (redissonClient != null && !redissonClient.isShutdown()) {
            try {
                redissonClient.shutdown();
                log.info("RedissonClient shutdown completed");
            } catch (Exception e) {
                log.warn("Error occurred while shutting down RedissonClient", e);
            }
        }
        
        this.initialized = false;
        this.healthy = false;
    }
    
    /**
     * 連線統計資訊
     */
    public static class ConnectionStats {
        private boolean initialized;
        private boolean healthy;
        private String connectionInfo;
        private String error;

        public ConnectionStats(boolean initialized, boolean healthy, String connectionInfo, String error) {
            this.initialized = initialized;
            this.healthy = healthy;
            this.connectionInfo = connectionInfo;
            this.error = error;
        }

        public boolean isInitialized() { return initialized; }
        public boolean isHealthy() { return healthy; }
        public String getConnectionInfo() { return connectionInfo; }
        public String getError() { return error; }
    }
}
