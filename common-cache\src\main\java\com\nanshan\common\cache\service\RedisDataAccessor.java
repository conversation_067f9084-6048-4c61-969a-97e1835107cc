package com.nanshan.common.cache.service;

import java.time.Duration;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis 資料存取介面
 * 
 * 提供 Redis 基本資料結構的通用操作介面
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface RedisDataAccessor {
    
    // ==================== String 操作 ====================
    
    /**
     * 設定字串值
     * 
     * @param key 鍵
     * @param value 值
     */
    void setString(String key, String value);
    
    /**
     * 設定字串值並指定過期時間
     * 
     * @param key 鍵
     * @param value 值
     * @param timeout 過期時間
     * @param timeUnit 時間單位
     */
    void setString(String key, String value, long timeout, TimeUnit timeUnit);
    
    /**
     * 設定字串值並指定過期時間
     * 
     * @param key 鍵
     * @param value 值
     * @param duration 過期時間
     */
    void setString(String key, String value, Duration duration);
    
    /**
     * 獲取字串值
     * 
     * @param key 鍵
     * @return 值的 Optional
     */
    Optional<String> getString(String key);
    
    /**
     * 刪除字串
     * 
     * @param key 鍵
     * @return 是否刪除成功
     */
    boolean deleteString(String key);
    
    // ==================== Object 操作 ====================
    
    /**
     * 設定物件值
     * 
     * @param key 鍵
     * @param value 值
     * @param <T> 值類型
     */
    <T> void setObject(String key, T value);
    
    /**
     * 設定物件值並指定過期時間
     * 
     * @param key 鍵
     * @param value 值
     * @param timeout 過期時間
     * @param timeUnit 時間單位
     * @param <T> 值類型
     */
    <T> void setObject(String key, T value, long timeout, TimeUnit timeUnit);
    
    /**
     * 設定物件值並指定過期時間
     * 
     * @param key 鍵
     * @param value 值
     * @param duration 過期時間
     * @param <T> 值類型
     */
    <T> void setObject(String key, T value, Duration duration);
    
    /**
     * 獲取物件值
     * 
     * @param key 鍵
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 值的 Optional
     */
    <T> Optional<T> getObject(String key, Class<T> clazz);
    
    /**
     * 刪除物件
     * 
     * @param key 鍵
     * @return 是否刪除成功
     */
    boolean deleteObject(String key);
    
    // ==================== Hash 操作 ====================
    
    /**
     * 設定 Hash 欄位值
     * 
     * @param key 鍵
     * @param field 欄位
     * @param value 值
     */
    void setHashField(String key, String field, Object value);
    
    /**
     * 獲取 Hash 欄位值
     * 
     * @param key 鍵
     * @param field 欄位
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 值的 Optional
     */
    <T> Optional<T> getHashField(String key, String field, Class<T> clazz);
    
    /**
     * 刪除 Hash 欄位
     * 
     * @param key 鍵
     * @param field 欄位
     * @return 是否刪除成功
     */
    boolean deleteHashField(String key, String field);
    
    /**
     * 獲取 Hash 所有欄位
     * 
     * @param key 鍵
     * @return 欄位值對應表
     */
    Map<String, Object> getHashAll(String key);
    
    /**
     * 設定 Hash 所有欄位
     * 
     * @param key 鍵
     * @param hash 欄位值對應表
     */
    void setHashAll(String key, Map<String, Object> hash);
    
    // ==================== Set 操作 ====================
    
    /**
     * 新增 Set 成員
     * 
     * @param key 鍵
     * @param values 值集合
     * @return 新增的成員數量
     */
    long addToSet(String key, Object... values);
    
    /**
     * 移除 Set 成員
     * 
     * @param key 鍵
     * @param values 值集合
     * @return 移除的成員數量
     */
    long removeFromSet(String key, Object... values);
    
    /**
     * 檢查 Set 是否包含成員
     * 
     * @param key 鍵
     * @param value 值
     * @return 是否包含
     */
    boolean isSetMember(String key, Object value);
    
    /**
     * 獲取 Set 所有成員
     * 
     * @param key 鍵
     * @param clazz 成員類型
     * @param <T> 成員類型
     * @return 成員集合
     */
    <T> Set<T> getSetMembers(String key, Class<T> clazz);
    
    /**
     * 獲取 Set 大小
     * 
     * @param key 鍵
     * @return Set 大小
     */
    long getSetSize(String key);
    
    // ==================== List 操作 ====================
    
    /**
     * 從左側推入 List
     * 
     * @param key 鍵
     * @param values 值集合
     * @return List 大小
     */
    long leftPushToList(String key, Object... values);
    
    /**
     * 從右側推入 List
     * 
     * @param key 鍵
     * @param values 值集合
     * @return List 大小
     */
    long rightPushToList(String key, Object... values);
    
    /**
     * 從左側彈出 List
     * 
     * @param key 鍵
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 彈出的值
     */
    <T> Optional<T> leftPopFromList(String key, Class<T> clazz);
    
    /**
     * 從右側彈出 List
     * 
     * @param key 鍵
     * @param clazz 值類型
     * @param <T> 值類型
     * @return 彈出的值
     */
    <T> Optional<T> rightPopFromList(String key, Class<T> clazz);
    
    /**
     * 獲取 List 範圍內的元素
     * 
     * @param key 鍵
     * @param start 開始索引
     * @param end 結束索引
     * @param clazz 元素類型
     * @param <T> 元素類型
     * @return 元素列表
     */
    <T> List<T> getListRange(String key, long start, long end, Class<T> clazz);
    
    /**
     * 獲取 List 大小
     * 
     * @param key 鍵
     * @return List 大小
     */
    long getListSize(String key);
    
    // ==================== 通用操作 ====================
    
    /**
     * 檢查鍵是否存在
     * 
     * @param key 鍵
     * @return 是否存在
     */
    boolean exists(String key);
    
    /**
     * 刪除鍵
     * 
     * @param keys 鍵集合
     * @return 刪除的鍵數量
     */
    long delete(String... keys);
    
    /**
     * 設定鍵的過期時間
     * 
     * @param key 鍵
     * @param timeout 過期時間
     * @param timeUnit 時間單位
     * @return 是否設定成功
     */
    boolean expire(String key, long timeout, TimeUnit timeUnit);
    
    /**
     * 設定鍵的過期時間
     * 
     * @param key 鍵
     * @param duration 過期時間
     * @return 是否設定成功
     */
    boolean expire(String key, Duration duration);
    
    /**
     * 獲取鍵的剩餘過期時間
     * 
     * @param key 鍵
     * @param timeUnit 時間單位
     * @return 剩餘時間，-1 表示永不過期，-2 表示鍵不存在
     */
    long getExpire(String key, TimeUnit timeUnit);
    
    /**
     * 移除鍵的過期時間
     * 
     * @param key 鍵
     * @return 是否移除成功
     */
    boolean persist(String key);
    
    /**
     * 根據模式查找鍵
     *
     * @param pattern 模式
     * @return 符合模式的鍵集合
     */
    Set<String> keys(String pattern);

    /**
     * 清空當前資料庫的所有鍵 (對應 Redis FLUSHDB 指令)
     *
     * <p><strong>⚠️ 危險操作警告：</strong></p>
     * <ul>
     *   <li>此操作會刪除當前 Redis 資料庫中的所有鍵</li>
     *   <li>在生產環境中，此操作預設被禁用</li>
     *   <li>請謹慎使用，建議僅在開發或測試環境中使用</li>
     * </ul>
     *
     * @return 是否執行成功
     * @throws RedisOperationException 當 Redis 操作失敗時
     */
    boolean flushDatabase();

    /**
     * 清空所有資料庫的所有鍵 (對應 Redis FLUSHALL 指令)
     *
     * <p><strong>⚠️ 極度危險操作警告：</strong></p>
     * <ul>
     *   <li>此操作會刪除 Redis 伺服器上所有資料庫中的所有鍵</li>
     *   <li>在生產環境中，此操作預設被禁用</li>
     *   <li>此操作不可逆，請極度謹慎使用</li>
     *   <li>建議僅在開發環境的特殊情況下使用</li>
     * </ul>
     *
     * @return 是否執行成功
     * @throws RedisOperationException 當 Redis 操作失敗時
     */
    boolean flushAllDatabases();
}
