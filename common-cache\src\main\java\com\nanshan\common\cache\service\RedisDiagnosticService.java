package com.nanshan.common.cache.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.redisson.api.RLock;
import org.redisson.api.RTopic;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Redis 診斷服務
 *
 * 提供 Redis 連接和 PubSub 功能的診斷工具
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisDiagnosticService {

    private final RedissonClient redissonClient;

    /**
     * 執行完整的 Redis 診斷
     *
     * @return 診斷結果
     */
    public Map<String, Object> runFullDiagnostic() {
        Map<String, Object> results = new HashMap<>();

        log.info("開始執行 Redis 完整診斷...");

        // 基本連接測試
        results.put("basicConnection", testBasicConnection());

        // PubSub 功能測試
        results.put("pubSubConnection", testPubSubConnection());

        // 分散式鎖測試
        results.put("distributedLock", testDistributedLock());

        // 強制解鎖測試
        results.put("forceUnlock", testForceUnlock());

        log.info("Redis 診斷完成");
        return results;
    }

    /**
     * 測試基本 Redis 連接
     */
    public Map<String, Object> testBasicConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            // 測試基本的 Redis 操作
            var bucket = redissonClient.getBucket("diagnostic:test");
            bucket.set("test-value", 10, TimeUnit.SECONDS);
            String value = (String) bucket.get();

            result.put("success", "test-value".equals(value));
            result.put("message", "基本連接測試成功");
            result.put("value", value);

            // 清理測試數據
            bucket.delete();

        } catch (Exception e) {
            log.error("基本連接測試失敗", e);
            result.put("success", false);
            result.put("message", "基本連接測試失敗: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 測試 PubSub 連接
     */
    public Map<String, Object> testPubSubConnection() {
        Map<String, Object> result = new HashMap<>();

        try {
            RTopic topic = redissonClient.getTopic("diagnostic:pubsub:test");

            // 嘗試發布消息
            long receivedClients = topic.publish("test-message");

            result.put("success", true);
            result.put("message", "PubSub 連接測試成功");
            result.put("receivedClients", receivedClients);

        } catch (Exception e) {
            log.error("PubSub 連接測試失敗", e);
            result.put("success", false);
            result.put("message", "PubSub 連接測試失敗: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());

            // 檢查是否是權限問題
            if (e.getMessage() != null && e.getMessage().contains("NOPERM")) {
                result.put("diagnosis", "Redis PubSub 權限被拒絕，請檢查 Redis ACL 配置");
            }
        }

        return result;
    }

    /**
     * 測試分散式鎖功能
     */
    public Map<String, Object> testDistributedLock() {
        Map<String, Object> result = new HashMap<>();
        String lockKey = "diagnostic:lock:test";

        try {
            RLock lock = redissonClient.getLock(lockKey);

            // 嘗試獲取鎖
            boolean acquired = lock.tryLock(5, 10, TimeUnit.SECONDS);

            if (acquired) {
                try {
                    // 檢查鎖狀態
                    boolean isLocked = lock.isLocked();
                    boolean isHeldByCurrentThread = lock.isHeldByCurrentThread();

                    result.put("lockAcquired", true);
                    result.put("isLocked", isLocked);
                    result.put("isHeldByCurrentThread", isHeldByCurrentThread);

                    // 嘗試解鎖
                    lock.unlock();

                    result.put("unlockSuccess", true);
                    result.put("success", true);
                    result.put("message", "分散式鎖測試成功");

                } catch (Exception unlockError) {
                    log.error("解鎖失敗", unlockError);
                    result.put("unlockSuccess", false);
                    result.put("unlockError", unlockError.getMessage());
                    result.put("success", false);
                    result.put("message", "分散式鎖解鎖失敗: " + unlockError.getMessage());
                }
            } else {
                result.put("success", false);
                result.put("message", "無法獲取分散式鎖");
                result.put("lockAcquired", false);
            }

        } catch (Exception e) {
            log.error("分散式鎖測試失敗", e);
            result.put("success", false);
            result.put("message", "分散式鎖測試失敗: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 測試強制解鎖功能
     */
    public Map<String, Object> testForceUnlock() {
        Map<String, Object> result = new HashMap<>();
        String lockKey = "diagnostic:force:unlock:test";

        try {
            RLock lock = redissonClient.getLock(lockKey);

            // 先獲取鎖
            boolean acquired = lock.tryLock(5, 30, TimeUnit.SECONDS);

            if (acquired) {
                // 嘗試強制解鎖
                boolean forceUnlocked = lock.forceUnlock();

                result.put("lockAcquired", true);
                result.put("forceUnlockSuccess", forceUnlocked);
                result.put("success", forceUnlocked);
                result.put("message", forceUnlocked ? "強制解鎖測試成功" : "強制解鎖失敗");

            } else {
                result.put("success", false);
                result.put("message", "無法獲取鎖進行強制解鎖測試");
                result.put("lockAcquired", false);
            }

        } catch (Exception e) {
            log.error("強制解鎖測試失敗", e);
            result.put("success", false);
            result.put("message", "強制解鎖測試失敗: " + e.getMessage());
            result.put("error", e.getClass().getSimpleName());
        }

        return result;
    }

    /**
     * 獲取 Redis 連接信息
     */
    public Map<String, Object> getConnectionInfo() {
        Map<String, Object> info = new HashMap<>();

        try {
            var config = redissonClient.getConfig();
            var singleServerConfig = config.useSingleServer();

            info.put("address", singleServerConfig.getAddress());
            info.put("connectionPoolSize", singleServerConfig.getConnectionPoolSize());
            info.put("subscriptionConnectionPoolSize", singleServerConfig.getSubscriptionConnectionPoolSize());
            info.put("subscriptionsPerConnection", singleServerConfig.getSubscriptionsPerConnection());
            info.put("subscriptionTimeout", singleServerConfig.getSubscriptionTimeout());
            info.put("connectTimeout", singleServerConfig.getConnectTimeout());
            info.put("timeout", singleServerConfig.getTimeout());
            info.put("retryAttempts", singleServerConfig.getRetryAttempts());
            info.put("retryInterval", singleServerConfig.getRetryInterval());

        } catch (Exception e) {
            log.error("獲取連接信息失敗", e);
            info.put("error", e.getMessage());
        }

        return info;
    }
}
