package com.nanshan.common.cache.service;

import com.nanshan.common.cache.model.SessionObject;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Session 管理介面
 *
 * 提供 JWT Session 的儲存、查詢、刪除和 TTL 控制功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface SessionManager {

    /**
     * 儲存 Session
     *
     * @param jwtId JWT ID
     * @param session Session 物件
     * @param ttlSeconds TTL 秒數
     */
    void saveSession(String jwtId, SessionObject session, long ttlSeconds);

    /**
     * 儲存 Session (使用預設 TTL)
     *
     * @param jwtId JWT ID
     * @param session Session 物件
     */
    void saveSession(String jwtId, SessionObject session);

    /**
     * 儲存 Session
     *
     * @param jwtId JWT ID
     * @param session Session 物件
     * @param duration TTL 時間
     */
    void saveSession(String jwtId, SessionObject session, Duration duration);

    /**
     * 載入 Session
     *
     * @param jwtId JWT ID
     * @return Session 物件的 Optional
     */
    Optional<SessionObject> loadSession(String jwtId);

    /**
     * 刪除 Session
     *
     * @param jwtId JWT ID
     * @return 是否刪除成功
     */
    boolean deleteSession(String jwtId);

    /**
     * 檢查 Session 是否存在
     *
     * @param jwtId JWT ID
     * @return 是否存在
     */
    boolean existsSession(String jwtId);

    /**
     * 更新 Session 的最後活動時間
     *
     * @param jwtId JWT ID
     * @return 是否更新成功
     */
    boolean updateLastActiveTime(String jwtId);

    /**
     * 續期 Session
     *
     * @param jwtId JWT ID
     * @param ttlSeconds 新的 TTL 秒數
     * @return 是否續期成功
     */
    boolean renewSession(String jwtId, long ttlSeconds);

    /**
     * 續期 Session (使用預設 TTL)
     *
     * @param jwtId JWT ID
     * @return 是否續期成功
     */
    boolean renewSession(String jwtId);

    /**
     * 獲取 Session 的剩餘存活時間
     *
     * @param jwtId JWT ID
     * @param timeUnit 時間單位
     * @return 剩餘存活時間，-1 表示永不過期，-2 表示不存在
     */
    long getSessionRemainingTimeToLive(String jwtId, TimeUnit timeUnit);

    /**
     * 根據使用者 ID 查找所有 Session
     *
     * @param userId 使用者 ID
     * @return Session 列表
     */
    List<SessionObject> findSessionsByUserId(String userId);

    /**
     * 根據客戶端 ID 查找所有 Session
     *
     * @param clientId 客戶端 ID
     * @return Session 列表
     */
    List<SessionObject> findSessionsByClientId(String clientId);

    /**
     * 刪除使用者的所有 Session
     *
     * @param userId 使用者 ID
     * @return 刪除的 Session 數量
     */
    long deleteSessionsByUserId(String userId);

    /**
     * 刪除客戶端的所有 Session
     *
     * @param clientId 客戶端 ID
     * @return 刪除的 Session 數量
     */
    long deleteSessionsByClientId(String clientId);

    /**
     * 清理過期的 Session
     *
     * @return 清理的 Session 數量
     */
    long cleanupExpiredSessions();

    /**
     * 獲取活躍 Session 數量
     *
     * @return 活躍 Session 數量
     */
    long getActiveSessionCount();

    /**
     * 獲取 Session 統計資訊
     *
     * @return Session 統計資訊
     */
    SessionStats getSessionStats();

    /**
     * Session 統計資訊
     */
    interface SessionStats {

        /**
         * 總 Session 數量
         */
        long getTotalSessions();

        /**
         * 活躍 Session 數量
         */
        long getActiveSessions();

        /**
         * 過期 Session 數量
         */
        long getExpiredSessions();

        /**
         * 今日新增 Session 數量
         */
        long getTodayNewSessions();

        /**
         * 平均 Session 持續時間 (秒)
         */
        double getAverageSessionDuration();
    }
}
