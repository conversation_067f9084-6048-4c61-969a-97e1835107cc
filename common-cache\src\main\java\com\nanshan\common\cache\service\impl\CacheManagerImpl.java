package com.nanshan.common.cache.service.impl;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.exception.CacheOperationException;
import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.RedisDataAccessor;
import com.nanshan.common.cache.util.RedisKeyHelper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * 快取管理實現 - 優化版本
 *
 * 主要改進： 1. 添加 Redisson RMapCache 支援以提升效能 2. 優化 keys() 操作，減少效能瓶頸 3. 改進統計和監控功能
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class CacheManagerImpl implements CacheManager {

    private final RedisDataAccessor redisDataAccessor;
    private final CommonCacheProperties cacheProperties;

    // Redisson 優化組件 (可選)
    @Autowired(required = false)
    private RedissonClient redissonClient;

    // 使用 RMapCache 進行快取優化
    private Map<String, RMapCache<String, Object>> cacheMap = new HashMap<>();

    public CacheManagerImpl(RedisDataAccessor redisDataAccessor,
            CommonCacheProperties cacheProperties) {
        this.redisDataAccessor = redisDataAccessor;
        this.cacheProperties = cacheProperties;
    }

    // 統計計數器
    private final AtomicLong hitCount = new AtomicLong(0);
    private final AtomicLong missCount = new AtomicLong(0);

    @Override
    public <T> void put(String type, String id, T value) {
        long defaultTtl = cacheProperties.getCache().getDefaultTimeToLive();
        put(type, id, value, defaultTtl, TimeUnit.SECONDS);
    }

    @Override
    public <T> void put(String type, String id, T value, long ttl, TimeUnit timeUnit) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            redisDataAccessor.setObject(key, value, ttl, timeUnit);

            log.debug("Cache stored: type={}, id={}, ttl={}s", type, id,
                    timeUnit.toSeconds(ttl));
        } catch (Exception e) {
            log.error("Failed to store cache: type={}, id={}", type, id, e);
            throw new CacheOperationException(type + ":" + id, "put",
                    "Failed to store cache", e);
        }
    }

    @Override
    public <T> void put(String type, String id, T value, Duration duration) {
        put(type, id, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public <T> Optional<T> get(String type, String id, Class<T> clazz) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            Optional<T> result = redisDataAccessor.getObject(key, clazz);

            if (result.isPresent()) {
                hitCount.incrementAndGet();

                // 自動續期檢查
                if (shouldAutoRenew(type, id)) {
                    renew(type, id);
                }

                log.debug("Cache hit: type={}, id={}", type, id);
            } else {
                missCount.incrementAndGet();
                log.debug("Cache miss: type={}, id={}", type, id);
            }

            return result;
        } catch (Exception e) {
            missCount.incrementAndGet();
            log.error("Failed to get cache: type={}, id={}", type, id, e);
            throw new CacheOperationException(type + ":" + id, "get",
                    "Failed to get cache", e);
        }
    }

    @Override
    public boolean remove(String type, String id) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            boolean deleted = redisDataAccessor.deleteObject(key);

            if (deleted) {
                log.debug("Cache removed: type={}, id={}", type, id);
            } else {
                log.debug("Cache not found for removal: type={}, id={}", type, id);
            }

            return deleted;
        } catch (Exception e) {
            log.error("Failed to remove cache: type={}, id={}", type, id, e);
            throw new CacheOperationException(type + ":" + id, "remove",
                    "Failed to remove cache", e);
        }
    }

    @Override
    public long removeAll(String type) {
        try {
            String pattern = RedisKeyHelper.buildCacheKey(type, "*");
            Set<String> keys = redisDataAccessor.keys(pattern);

            if (keys.isEmpty()) {
                return 0;
            }

            long deletedCount = redisDataAccessor.delete(keys.toArray(new String[0]));
            log.debug("Removed {} caches for type: {}", deletedCount, type);

            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to remove all caches for type: {}", type, e);
            throw new CacheOperationException(type, "removeAll",
                    "Failed to remove all caches", e);
        }
    }

    @Override
    public boolean exists(String type, String id) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            return redisDataAccessor.exists(key);
        } catch (Exception e) {
            log.error("Failed to check cache existence: type={}, id={}", type, id, e);
            return false;
        }
    }

    @Override
    public long getRemainingTimeToLive(String type, String id, TimeUnit timeUnit) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            return redisDataAccessor.getExpire(key, timeUnit);
        } catch (Exception e) {
            log.error("Failed to get remaining time to live: type={}, id={}", type, id, e);
            return -2; // 表示錯誤
        }
    }

    @Override
    public boolean setTimeToLive(String type, String id, long ttl, TimeUnit timeUnit) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            boolean result = redisDataAccessor.expire(key, ttl, timeUnit);

            if (result) {
                log.debug("Cache TTL set: type={}, id={}, ttl={}s", type, id,
                        timeUnit.toSeconds(ttl));
            }

            return result;
        } catch (Exception e) {
            log.error("Failed to set cache TTL: type={}, id={}", type, id, e);
            return false;
        }
    }

    @Override
    public boolean renew(String type, String id, long ttl, TimeUnit timeUnit) {
        try {
            String key = RedisKeyHelper.buildCacheKey(type, id);
            boolean renewed = redisDataAccessor.expire(key, ttl, timeUnit);

            if (renewed) {
                log.debug("Cache renewed: type={}, id={}, ttl={}s", type, id,
                        timeUnit.toSeconds(ttl));
            }

            return renewed;
        } catch (Exception e) {
            log.error("Failed to renew cache: type={}, id={}", type, id, e);
            return false;
        }
    }

    @Override
    public boolean renew(String type, String id) {
        long defaultTtl = cacheProperties.getCache().getDefaultTimeToLive();
        return renew(type, id, defaultTtl, TimeUnit.SECONDS);
    }

    @Override
    public Set<String> getAllCacheIds(String type) {
        try {
            String pattern = RedisKeyHelper.buildCacheKey(type, "*");
            Set<String> keys = redisDataAccessor.keys(pattern);

            // 提取 ID 部分
            Set<String> ids = new HashSet<>();
            String prefix = RedisKeyHelper.buildCacheKey(type, "");

            for (String key : keys) {
                if (key.startsWith(prefix)) {
                    String id = key.substring(prefix.length());
                    ids.add(id);
                }
            }

            return ids;
        } catch (Exception e) {
            log.error("Failed to get cache IDs for type: {}", type, e);
            return new HashSet<>();
        }
    }

    @Override
    public <T> Map<String, T> multiGet(String type, Collection<String> ids, Class<T> clazz) {
        Map<String, T> result = new HashMap<>();

        for (String id : ids) {
            Optional<T> value = get(type, id, clazz);
            if (value.isPresent()) {
                result.put(id, value.get());
            }
        }

        return result;
    }

    @Override
    public <T> void multiPut(String type, Map<String, T> data) {
        long defaultTtl = cacheProperties.getCache().getDefaultTimeToLive();
        multiPut(type, data, defaultTtl, TimeUnit.SECONDS);
    }

    @Override
    public <T> void multiPut(String type, Map<String, T> data, long ttl, TimeUnit timeUnit) {
        for (Map.Entry<String, T> entry : data.entrySet()) {
            put(type, entry.getKey(), entry.getValue(), ttl, timeUnit);
        }
    }

    @Override
    public long multiRemove(String type, Collection<String> ids) {
        long removedCount = 0;

        for (String id : ids) {
            if (remove(type, id)) {
                removedCount++;
            }
        }

        return removedCount;
    }

    @Override
    public long cleanupExpired() {
        try {
            String pattern = RedisKeyHelper.buildCacheKey("*", "*");
            Set<String> keys = redisDataAccessor.keys(pattern);

            long cleanedCount = 0;
            for (String key : keys) {
                try {
                    long ttl = redisDataAccessor.getExpire(key, TimeUnit.SECONDS);
                    if (ttl == -2) { // Key 不存在或已過期
                        cleanedCount++;
                    }
                } catch (Exception e) {
                    log.warn("Failed to check TTL for key: {}", key, e);
                }
            }

            log.debug("Cleaned up {} expired caches", cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("Failed to cleanup expired caches", e);
            return 0;
        }
    }

    // ==================== 私有輔助方法 ====================
    /**
     * 檢查是否應該自動續期
     */
    private boolean shouldAutoRenew(String type, String id) {
        if (!cacheProperties.getCache().isAutoRenewal()) {
            return false;
        }

        try {
            long remainingTtl = getRemainingTimeToLive(type, id, TimeUnit.SECONDS);
            long defaultTtl = cacheProperties.getCache().getDefaultTimeToLive();
            double threshold = cacheProperties.getCache().getRenewalThreshold();

            return remainingTtl > 0 && remainingTtl < (defaultTtl * threshold);
        } catch (Exception e) {
            log.warn("Failed to check auto renewal for cache: type={}, id={}", type, id, e);
            return false;
        }
    }

    @Override
    public CacheStats getStats() {
        return new CacheStatsImpl();
    }

    /**
     * 快取統計資訊實現
     */
    private class CacheStatsImpl implements CacheStats {

        @Override
        public long getHitCount() {
            return hitCount.get();
        }

        @Override
        public long getMissCount() {
            return missCount.get();
        }

        @Override
        public double getHitRate() {
            long hits = getHitCount();
            long misses = getMissCount();
            long total = hits + misses;

            return total > 0 ? (double) hits / total : 0.0;
        }

        @Override
        public long getTotalRequests() {
            return getHitCount() + getMissCount();
        }

    }
}
