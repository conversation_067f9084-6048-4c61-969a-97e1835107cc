package com.nanshan.common.cache.service.impl;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.service.CacheManager;
import com.nanshan.common.cache.service.RedisCleaner;
import com.nanshan.common.cache.service.RedisDataAccessor;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.common.cache.util.RedisKeyHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis 清理服務實現
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(prefix = "common.cache.cleaner", name = "enabled", havingValue = "true", matchIfMissing = true)
public class RedisCleanerImpl implements RedisCleaner {

    private final RedisDataAccessor redisDataAccessor;
    private final SessionManager sessionManager;
    private final CacheManager cacheManager;
    private final CommonCacheProperties cacheProperties;
    private final Environment environment;

    // 統計計數器
    private final AtomicLong totalCleanups = new AtomicLong(0);
    private final AtomicLong totalSessionsCleaned = new AtomicLong(0);
    private final AtomicLong totalCachesCleaned = new AtomicLong(0);
    private final AtomicLong totalLocksCleaned = new AtomicLong(0);
    private final AtomicLong totalCleanupDuration = new AtomicLong(0);
    private volatile LocalDateTime lastCleanupTime;

    /**
     * 定時清理任務
     */
    @Scheduled(fixedDelayString = "#{${common.cache.cleaner.schedule-interval:600} * 1000}")
    public void scheduledCleanup() {
        if (cacheProperties.getCleaner().isEnabled()) {
            log.debug("Starting scheduled cleanup...");
            CleanupResult result = performManualCleanup();
            log.info("Scheduled cleanup completed: sessions={}, caches={}, locks={}, duration={}ms",
                    result.getSessionsCleaned(), result.getCachesCleaned(),
                    result.getLocksCleaned(), result.getDurationMs());
        }
    }

    @Override
    public long cleanupExpiredSessions() {
        try {
            long cleaned = sessionManager.cleanupExpiredSessions();
            totalSessionsCleaned.addAndGet(cleaned);
            log.debug("Cleaned {} expired sessions", cleaned);
            return cleaned;
        } catch (Exception e) {
            log.error("Failed to cleanup expired sessions", e);
            return 0;
        }
    }

    @Override
    public long cleanupExpiredCaches() {
        try {
            long cleaned = cacheManager.cleanupExpired();
            totalCachesCleaned.addAndGet(cleaned);
            log.debug("Cleaned {} expired caches", cleaned);
            return cleaned;
        } catch (Exception e) {
            log.error("Failed to cleanup expired caches", e);
            return 0;
        }
    }

    @Override
    public long cleanupExpiredLocks() {
        try {
            String lockPattern = RedisKeyHelper.buildLockKey("*");
            Set<String> lockKeys = redisDataAccessor.keys(lockPattern);

            long cleaned = 0;
            for (String key : lockKeys) {
                try {
                    long ttl = redisDataAccessor.getExpire(key, TimeUnit.SECONDS);
                    if (ttl == -2) { // Key 不存在或已過期
                        if (redisDataAccessor.delete(key) > 0) {
                            cleaned++;
                        }
                    }
                } catch (Exception e) {
                    log.warn("Failed to check/clean lock key: {}", key, e);
                }
            }

            totalLocksCleaned.addAndGet(cleaned);
            log.debug("Cleaned {} expired locks", cleaned);
            return cleaned;
        } catch (Exception e) {
            log.error("Failed to cleanup expired locks", e);
            return 0;
        }
    }

    @Override
    public long cleanupAllExpired() {
        long sessionsCleaned = cleanupExpiredSessions();
        long cachesCleaned = cleanupExpiredCaches();
        long locksCleaned = cleanupExpiredLocks();

        long total = sessionsCleaned + cachesCleaned + locksCleaned;
        log.debug("Total cleanup: sessions={}, caches={}, locks={}, total={}",
                sessionsCleaned, cachesCleaned, locksCleaned, total);

        return total;
    }

    @Override
    public long deleteByPattern(String pattern) {
        try {
            // 檢查是否為生產環境，避免使用危險的 keys 操作
            if (isProductionEnvironment() && !cacheProperties.getEnvGuard().isDangerousOperationsEnabled()) {
                log.warn("Pattern deletion '{}' is disabled in production environment", pattern);
                return 0;
            }

            Set<String> keys = redisDataAccessor.keys(pattern);
            if (keys.isEmpty()) {
                return 0;
            }

            long deleted = redisDataAccessor.delete(keys.toArray(new String[0]));
            log.debug("Deleted {} keys with pattern: {}", deleted, pattern);

            return deleted;
        } catch (Exception e) {
            log.error("Failed to delete keys by pattern: {}", pattern, e);
            return 0;
        }
    }

    @Override
    public long deleteBatch(Set<String> keys) {
        try {
            if (keys.isEmpty()) {
                return 0;
            }

            long deleted = redisDataAccessor.delete(keys.toArray(new String[0]));
            log.debug("Batch deleted {} keys", deleted);

            return deleted;
        } catch (Exception e) {
            log.error("Failed to batch delete keys", e);
            return 0;
        }
    }

    @Override
    public boolean setTimeToLive(String key, long timeToLiveSeconds) {
        try {
            boolean success = redisDataAccessor.expire(key, timeToLiveSeconds, TimeUnit.SECONDS);
            if (success) {
                log.debug("Set time to live for key: {}, timeToLive={}s", key, timeToLiveSeconds);
            }
            return success;
        } catch (Exception e) {
            log.error("Failed to set time to live for key: {}", key, e);
            return false;
        }
    }

    @Override
    public long setBatchTimeToLive(Set<String> keys, long timeToLiveSeconds) {
        long successCount = 0;

        for (String key : keys) {
            if (setTimeToLive(key, timeToLiveSeconds)) {
                successCount++;
            }
        }

        log.debug("Set time to live for {}/{} keys, timeToLive={}s", successCount, keys.size(), timeToLiveSeconds);
        return successCount;
    }

    @Override
    public boolean removeTimeToLive(String key) {
        try {
            boolean success = redisDataAccessor.persist(key);
            if (success) {
                log.debug("Removed time to live for key: {}", key);
            }
            return success;
        } catch (Exception e) {
            log.error("Failed to remove time to live for key: {}", key, e);
            return false;
        }
    }

    @Override
    public CleanupStats getCleanupStats() {
        return new CleanupStatsImpl();
    }

    @Override
    public void resetCleanupStats() {
        totalCleanups.set(0);
        totalSessionsCleaned.set(0);
        totalCachesCleaned.set(0);
        totalLocksCleaned.set(0);
        totalCleanupDuration.set(0);
        lastCleanupTime = null;
        log.debug("Cleanup statistics reset");
    }

    @Override
    public CleanupResult performManualCleanup() {
        long startTime = System.currentTimeMillis();

        try {
            long sessionsCleaned = cleanupExpiredSessions();
            long cachesCleaned = cleanupExpiredCaches();
            long locksCleaned = cleanupExpiredLocks();

            long duration = System.currentTimeMillis() - startTime;

            // 更新統計
            totalCleanups.incrementAndGet();
            totalCleanupDuration.addAndGet(duration);
            lastCleanupTime = LocalDateTime.now();

            return new CleanupResultImpl(sessionsCleaned, cachesCleaned, locksCleaned,
                    duration, true, null);
        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("Manual cleanup failed", e);

            return new CleanupResultImpl(0, 0, 0, duration, false, e.getMessage());
        }
    }

    /**
     * 檢查是否為生產環境
     */
    private boolean isProductionEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        List<String> productionProfiles = cacheProperties.getEnvGuard().getProductionProfiles();

        return Arrays.stream(activeProfiles)
                .anyMatch(productionProfiles::contains);
    }

    /**
     * 清理統計資訊實現
     */
    private class CleanupStatsImpl implements CleanupStats {

        @Override
        public long getTotalCleanups() {
            return totalCleanups.get();
        }

        @Override
        public long getTotalSessionsCleaned() {
            return totalSessionsCleaned.get();
        }

        @Override
        public long getTotalCachesCleaned() {
            return totalCachesCleaned.get();
        }

        @Override
        public long getTotalLocksCleaned() {
            return totalLocksCleaned.get();
        }

        @Override
        public LocalDateTime getLastCleanupTime() {
            return lastCleanupTime;
        }

        @Override
        public double getAverageCleanupDuration() {
            long cleanups = totalCleanups.get();
            return cleanups > 0 ? (double) totalCleanupDuration.get() / cleanups : 0.0;
        }
    }

    /**
     * 清理結果實現
     */
    private static class CleanupResultImpl implements CleanupResult {

        private final long sessionsCleaned;
        private final long cachesCleaned;
        private final long locksCleaned;
        private final long durationMs;
        private final boolean success;
        private final String errorMessage;

        public CleanupResultImpl(long sessionsCleaned, long cachesCleaned, long locksCleaned,
                long durationMs, boolean success, String errorMessage) {
            this.sessionsCleaned = sessionsCleaned;
            this.cachesCleaned = cachesCleaned;
            this.locksCleaned = locksCleaned;
            this.durationMs = durationMs;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        @Override
        public long getSessionsCleaned() {
            return sessionsCleaned;
        }

        @Override
        public long getCachesCleaned() {
            return cachesCleaned;
        }

        @Override
        public long getLocksCleaned() {
            return locksCleaned;
        }

        @Override
        public long getDurationMs() {
            return durationMs;
        }

        @Override
        public boolean isSuccess() {
            return success;
        }

        @Override
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
