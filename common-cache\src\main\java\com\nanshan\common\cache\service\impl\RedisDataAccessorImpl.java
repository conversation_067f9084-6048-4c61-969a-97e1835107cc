package com.nanshan.common.cache.service.impl;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.exception.RedisOperationException;
import com.nanshan.common.cache.service.RedisDataAccessor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RList;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Redis 資料存取實現
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RedisDataAccessorImpl implements RedisDataAccessor {

    private final RedissonClient redissonClient;
    private final CommonCacheProperties cacheProperties;
    private final Environment environment;

    // ==================== String 操作 ====================
    @Override
    public void setString(String key, String value) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
            bucket.set(value);
        } catch (Exception e) {
            log.error("Failed to set string value for key: {}", key, e);
            throw new RedisOperationException("setString", key, "Failed to set string value", e);
        }
    }

    @Override
    public void setString(String key, String value, long timeout, TimeUnit timeUnit) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
            bucket.set(value, timeout, timeUnit);
        } catch (Exception e) {
            log.error("Failed to set string value with TTL for key: {}", key, e);
            throw new RedisOperationException("setString", key, "Failed to set string value with TTL", e);
        }
    }

    @Override
    public void setString(String key, String value, Duration duration) {
        setString(key, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public Optional<String> getString(String key) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
            return Optional.ofNullable(bucket.get());
        } catch (Exception e) {
            log.error("Failed to get string value for key: {}", key, e);
            throw new RedisOperationException("getString", key, "Failed to get string value", e);
        }
    }

    @Override
    public boolean deleteString(String key) {
        try {
            RBucket<String> bucket = redissonClient.getBucket(key, StringCodec.INSTANCE);
            return bucket.delete();
        } catch (Exception e) {
            log.error("Failed to delete string for key: {}", key, e);
            throw new RedisOperationException("deleteString", key, "Failed to delete string", e);
        }
    }

    // ==================== Object 操作 ====================
    @Override
    public <T> void setObject(String key, T value) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            bucket.set(value);
        } catch (Exception e) {
            log.error("Failed to set object value for key: {}", key, e);
            throw new RedisOperationException("setObject", key, "Failed to set object value", e);
        }
    }

    @Override
    public <T> void setObject(String key, T value, long timeout, TimeUnit timeUnit) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            bucket.set(value, timeout, timeUnit);
        } catch (Exception e) {
            log.error("Failed to set object value with TTL for key: {}", key, e);
            throw new RedisOperationException("setObject", key, "Failed to set object value with TTL", e);
        }
    }

    @Override
    public <T> void setObject(String key, T value, Duration duration) {
        setObject(key, value, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public <T> Optional<T> getObject(String key, Class<T> clazz) {
        try {
            RBucket<T> bucket = redissonClient.getBucket(key);
            return Optional.ofNullable(bucket.get());
        } catch (Exception e) {
            log.error("Failed to get object value for key: {}", key, e);
            throw new RedisOperationException("getObject", key, "Failed to get object value", e);
        }
    }

    @Override
    public boolean deleteObject(String key) {
        try {
            RBucket<?> bucket = redissonClient.getBucket(key);
            return bucket.delete();
        } catch (Exception e) {
            log.error("Failed to delete object for key: {}", key, e);
            throw new RedisOperationException("deleteObject", key, "Failed to delete object", e);
        }
    }

    // ==================== Hash 操作 ====================
    @Override
    public void setHashField(String key, String field, Object value) {
        try {
            RMap<String, Object> hash = redissonClient.getMap(key);
            hash.put(field, value);
        } catch (Exception e) {
            log.error("Failed to set hash field for key: {}, field: {}", key, field, e);
            throw new RedisOperationException("setHashField", key, "Failed to set hash field: " + field, e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getHashField(String key, String field, Class<T> clazz) {
        try {
            RMap<String, Object> hash = redissonClient.getMap(key);
            Object value = hash.get(field);
            if (value == null) {
                return Optional.empty();
            }
            return Optional.of((T) value);
        } catch (Exception e) {
            log.error("Failed to get hash field for key: {}, field: {}", key, field, e);
            throw new RedisOperationException("getHashField", key, "Failed to get hash field: " + field, e);
        }
    }

    @Override
    public boolean deleteHashField(String key, String field) {
        try {
            RMap<String, Object> hash = redissonClient.getMap(key);
            return hash.remove(field) != null;
        } catch (Exception e) {
            log.error("Failed to delete hash field for key: {}, field: {}", key, field, e);
            throw new RedisOperationException("deleteHashField", key, "Failed to delete hash field: " + field, e);
        }
    }

    @Override
    public Map<String, Object> getHashAll(String key) {
        try {
            RMap<String, Object> hash = redissonClient.getMap(key);
            return new HashMap<>(hash.readAllMap());
        } catch (Exception e) {
            log.error("Failed to get all hash fields for key: {}", key, e);
            throw new RedisOperationException("getHashAll", key, "Failed to get all hash fields", e);
        }
    }

    @Override
    public void setHashAll(String key, Map<String, Object> hash) {
        try {
            RMap<String, Object> redisHash = redissonClient.getMap(key);
            redisHash.putAll(hash);
        } catch (Exception e) {
            log.error("Failed to set all hash fields for key: {}", key, e);
            throw new RedisOperationException("setHashAll", key, "Failed to set all hash fields", e);
        }
    }

    // ==================== Set 操作 ====================
    @Override
    public long addToSet(String key, Object... values) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            return set.addAll(Arrays.asList(values)) ? values.length : 0;
        } catch (Exception e) {
            log.error("Failed to add to set for key: {}", key, e);
            throw new RedisOperationException("addToSet", key, "Failed to add to set", e);
        }
    }

    @Override
    public long removeFromSet(String key, Object... values) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            long removed = 0;
            for (Object value : values) {
                if (set.remove(value)) {
                    removed++;
                }
            }
            return removed;
        } catch (Exception e) {
            log.error("Failed to remove from set for key: {}", key, e);
            throw new RedisOperationException("removeFromSet", key, "Failed to remove from set", e);
        }
    }

    @Override
    public boolean isSetMember(String key, Object value) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            return set.contains(value);
        } catch (Exception e) {
            log.error("Failed to check set membership for key: {}", key, e);
            throw new RedisOperationException("isSetMember", key, "Failed to check set membership", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Set<T> getSetMembers(String key, Class<T> clazz) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            return set.readAll().stream()
                    .map(obj -> (T) obj)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("Failed to get set members for key: {}", key, e);
            throw new RedisOperationException("getSetMembers", key, "Failed to get set members", e);
        }
    }

    @Override
    public long getSetSize(String key) {
        try {
            RSet<Object> set = redissonClient.getSet(key);
            return set.size();
        } catch (Exception e) {
            log.error("Failed to get set size for key: {}", key, e);
            throw new RedisOperationException("getSetSize", key, "Failed to get set size", e);
        }
    }

    // ==================== List 操作 ====================
    @Override
    public long leftPushToList(String key, Object... values) {
        try {
            RList<Object> list = redissonClient.getList(key);
            for (int i = values.length - 1; i >= 0; i--) {
                list.add(0, values[i]);
            }
            return list.size();
        } catch (Exception e) {
            log.error("Failed to left push to list for key: {}", key, e);
            throw new RedisOperationException("leftPushToList", key, "Failed to left push to list", e);
        }
    }

    @Override
    public long rightPushToList(String key, Object... values) {
        try {
            RList<Object> list = redissonClient.getList(key);
            list.addAll(Arrays.asList(values));
            return list.size();
        } catch (Exception e) {
            log.error("Failed to right push to list for key: {}", key, e);
            throw new RedisOperationException("rightPushToList", key, "Failed to right push to list", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> leftPopFromList(String key, Class<T> clazz) {
        try {
            RList<Object> list = redissonClient.getList(key);
            if (list.isEmpty()) {
                return Optional.empty();
            }
            Object value = list.remove(0);
            return Optional.ofNullable((T) value);
        } catch (Exception e) {
            log.error("Failed to left pop from list for key: {}", key, e);
            throw new RedisOperationException("leftPopFromList", key, "Failed to left pop from list", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> Optional<T> rightPopFromList(String key, Class<T> clazz) {
        try {
            RList<Object> list = redissonClient.getList(key);
            if (list.isEmpty()) {
                return Optional.empty();
            }
            Object value = list.remove(list.size() - 1);
            return Optional.ofNullable((T) value);
        } catch (Exception e) {
            log.error("Failed to right pop from list for key: {}", key, e);
            throw new RedisOperationException("rightPopFromList", key, "Failed to right pop from list", e);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> List<T> getListRange(String key, long start, long end, Class<T> clazz) {
        try {
            RList<Object> list = redissonClient.getList(key);
            return list.range((int) start, (int) end).stream()
                    .map(obj -> (T) obj)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get list range for key: {}", key, e);
            throw new RedisOperationException("getListRange", key, "Failed to get list range", e);
        }
    }

    @Override
    public long getListSize(String key) {
        try {
            RList<Object> list = redissonClient.getList(key);
            return list.size();
        } catch (Exception e) {
            log.error("Failed to get list size for key: {}", key, e);
            throw new RedisOperationException("getListSize", key, "Failed to get list size", e);
        }
    }

    // ==================== 通用操作 ====================
    @Override
    public boolean exists(String key) {
        try {
            return redissonClient.getBucket(key).isExists();
        } catch (Exception e) {
            log.error("Failed to check existence for key: {}", key, e);
            throw new RedisOperationException("exists", key, "Failed to check existence", e);
        }
    }

    @Override
    public long delete(String... keys) {
        try {
            return redissonClient.getKeys().delete(keys);
        } catch (Exception e) {
            log.error("Failed to delete keys: {}", Arrays.toString(keys), e);
            throw new RedisOperationException("delete", Arrays.toString(keys), "Failed to delete keys", e);
        }
    }

    @Override
    public boolean expire(String key, long timeout, TimeUnit timeUnit) {
        try {
            return redissonClient.getBucket(key).expire(timeout, timeUnit);
        } catch (Exception e) {
            log.error("Failed to set expiration for key: {}", key, e);
            throw new RedisOperationException("expire", key, "Failed to set expiration", e);
        }
    }

    @Override
    public boolean expire(String key, Duration duration) {
        return expire(key, duration.toMillis(), TimeUnit.MILLISECONDS);
    }

    @Override
    public long getExpire(String key, TimeUnit timeUnit) {
        try {
            long remainTimeMillis = redissonClient.getBucket(key).remainTimeToLive();
            if (remainTimeMillis == -1 || remainTimeMillis == -2) {
                // -1: 永不過期, -2: 鍵不存在
                return remainTimeMillis;
            }
            // 將毫秒轉換為指定的時間單位
            return timeUnit.convert(remainTimeMillis, TimeUnit.MILLISECONDS);
        } catch (Exception e) {
            log.error("Failed to get expiration for key: {}", key, e);
            throw new RedisOperationException("getExpire", key, "Failed to get expiration", e);
        }
    }

    @Override
    public boolean persist(String key) {
        try {
            return redissonClient.getBucket(key).clearExpire();
        } catch (Exception e) {
            log.error("Failed to persist key: {}", key, e);
            throw new RedisOperationException("persist", key, "Failed to persist key", e);
        }
    }

    @Override
    public Set<String> keys(String pattern) {
        try {
            // 檢查是否為生產環境，避免使用危險的 keys 操作
            if (isProductionEnvironment() && !cacheProperties.getEnvGuard().isDangerousOperationsEnabled()) {
                log.warn("Keys operation with pattern '{}' is disabled in production environment", pattern);
                return Collections.emptySet();
            }

            Iterable<String> keys = redissonClient.getKeys().getKeysByPattern(pattern);
            Set<String> result = new HashSet<>();
            keys.forEach(result::add);
            return result;
        } catch (Exception e) {
            log.error("Failed to get keys with pattern: {}", pattern, e);
            throw new RedisOperationException("keys", pattern, "Failed to get keys with pattern", e);
        }
    }

    @Override
    public boolean flushDatabase() {
        try {
            // 檢查是否為生產環境，避免使用危險的 flushdb 操作
            if (isProductionEnvironment() && !cacheProperties.getEnvGuard().isDangerousOperationsEnabled()) {
                log.warn("FLUSHDB operation is disabled in production environment for safety. "
                        + "Current profile: {}, dangerous-operations-enabled: {}",
                        Arrays.toString(environment.getActiveProfiles()),
                        cacheProperties.getEnvGuard().isDangerousOperationsEnabled());
                return false;
            }

            log.warn("Executing FLUSHDB operation - this will delete all keys in current database!");
            redissonClient.getKeys().flushdb();
            log.info("FLUSHDB operation completed successfully");
            return true;
        } catch (Exception e) {
            log.error("Failed to execute FLUSHDB operation", e);
            throw new RedisOperationException("flushDatabase", "current-db",
                    "Failed to flush current database", e);
        }
    }

    @Override
    public boolean flushAllDatabases() {
        try {
            // 檢查是否為生產環境，避免使用極度危險的 flushall 操作
            if (isProductionEnvironment() && !cacheProperties.getEnvGuard().isDangerousOperationsEnabled()) {
                log.warn("FLUSHALL operation is disabled in production environment for safety. "
                        + "Current profile: {}, dangerous-operations-enabled: {}",
                        Arrays.toString(environment.getActiveProfiles()),
                        cacheProperties.getEnvGuard().isDangerousOperationsEnabled());
                return false;
            }

            log.warn("Executing FLUSHALL operation - this will delete ALL keys in ALL databases!");
            redissonClient.getKeys().flushall();
            log.info("FLUSHALL operation completed successfully");
            return true;
        } catch (Exception e) {
            log.error("Failed to execute FLUSHALL operation", e);
            throw new RedisOperationException("flushAllDatabases", "all-databases",
                    "Failed to flush all databases", e);
        }
    }

    /**
     * 檢查是否為生產環境
     *
     * @return 是否為生產環境
     */
    private boolean isProductionEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        List<String> productionProfiles = cacheProperties.getEnvGuard().getProductionProfiles();

        return Arrays.stream(activeProfiles)
                .anyMatch(productionProfiles::contains);
    }
}
