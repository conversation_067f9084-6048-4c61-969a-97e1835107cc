package com.nanshan.common.cache.service.impl;

import com.nanshan.common.cache.config.CommonCacheProperties;
import com.nanshan.common.cache.exception.SessionNotFoundException;
import com.nanshan.common.cache.model.SessionObject;
import com.nanshan.common.cache.service.RedisDataAccessor;
import com.nanshan.common.cache.service.SessionManager;
import com.nanshan.common.cache.util.RedisKeyHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMapCache;
import org.redisson.api.RMultimap;
import org.redisson.api.RedissonClient;
import org.redisson.api.map.event.EntryExpiredListener;
import org.redisson.api.map.event.EntryEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * Session 管理實現 - 優化版本
 *
 * 主要改進： 1. 使用 RMapCache 自動管理 TTL 和統計 2. 使用 RMultimap 管理索引，避免 keys() 操作 3.
 * 利用事件監聽器自動清理索引 4. 更好的效能和記憶體管理
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class SessionManagerImpl implements SessionManager {

    private final RedisDataAccessor redisDataAccessor;
    private final CommonCacheProperties cacheProperties;

    // Redisson 優化組件 (可選)
    @Autowired(required = false)
    private RedissonClient redissonClient;

    // 使用 RMapCache 自動管理 TTL 和統計
    private RMapCache<String, SessionObject> sessionCache;

    // 使用 RMultimap 管理索引，效能更好
    private RMultimap<String, String> userIdIndex;
    private RMultimap<String, String> clientIdIndex;

    // 統計計數器
    private final AtomicLong totalSessionsCounter = new AtomicLong(0);
    private final AtomicLong activeSessionsCounter = new AtomicLong(0);

    // 常數定義
    private static final String SESSION_INDEX_PREFIX = "session_index";
    private static final String USER_INDEX_TYPE = "user";
    private static final String CLIENT_INDEX_TYPE = "client";
    private static final String SESSION_STATS_PREFIX = "session_stats";

    public SessionManagerImpl(RedisDataAccessor redisDataAccessor,
            CommonCacheProperties cacheProperties) {
        this.redisDataAccessor = redisDataAccessor;
        this.cacheProperties = cacheProperties;
    }

    @PostConstruct
    public void initializeRedissonOptimizations() {
        if (redissonClient != null) {
            try {
                // 初始化 RMapCache
                sessionCache = redissonClient.getMapCache("sessions");

                // 初始化索引
                userIdIndex = redissonClient.getSetMultimap(SESSION_INDEX_PREFIX + ":" + USER_INDEX_TYPE);
                clientIdIndex = redissonClient.getSetMultimap(SESSION_INDEX_PREFIX + ":" + CLIENT_INDEX_TYPE);

                // 註冊過期事件監聽器，自動清理索引
                sessionCache.addListener(new EntryExpiredListener<String, SessionObject>() {
                    @Override
                    public void onExpired(EntryEvent<String, SessionObject> event) {
                        cleanupIndexesForExpiredSession(event.getKey(), event.getValue());
                    }
                });

                log.info("SessionManager Redisson optimizations initialized successfully");
            } catch (Exception e) {
                log.warn("Failed to initialize Redisson optimizations, falling back to RedisDataAccessor", e);
                sessionCache = null;
                userIdIndex = null;
                clientIdIndex = null;
            }
        } else {
            log.info("RedissonClient not available, using RedisDataAccessor only");
        }
    }

    @Override
    public void saveSession(String jwtId, SessionObject session, long ttlSeconds) {
        try {
            // 更新 Session 的時間戳記
            session.setUpdatedAt(LocalDateTime.now());
            if (session.getCreatedAt() == null) {
                session.setCreatedAt(LocalDateTime.now());
            }

            // 優先使用 RMapCache，否則使用 RedisDataAccessor
            if (sessionCache != null) {
                // 使用 RMapCache 自動管理 TTL
                sessionCache.put(jwtId, session, ttlSeconds, TimeUnit.SECONDS);

                // 更新索引
                updateIndexesWithRedisson(jwtId, session, ttlSeconds);

                // 更新統計計數器
                totalSessionsCounter.incrementAndGet();
                activeSessionsCounter.incrementAndGet();
            } else {
                // 回退到傳統方式
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);
                redisDataAccessor.setObject(key, session, ttlSeconds, TimeUnit.SECONDS);

                // 建立索引 (用於根據 userId 和 clientId 查詢)
                createSessionIndexes(jwtId, session, ttlSeconds);
            }

            log.debug("Session saved successfully: jwtId={}, userId={}, ttl={}s",
                    jwtId, session.getUserId(), ttlSeconds);
        } catch (Exception e) {
            log.error("Failed to save session: jwtId={}", jwtId, e);
            throw new RuntimeException("Failed to save session", e);
        }
    }

    @Override
    public void saveSession(String jwtId, SessionObject session) {
        long defaultTtl = cacheProperties.getSession().getDefaultTimeToLive();
        saveSession(jwtId, session, defaultTtl);
    }

    @Override
    public void saveSession(String jwtId, SessionObject session, Duration duration) {
        saveSession(jwtId, session, duration.getSeconds());
    }

    @Override
    public Optional<SessionObject> loadSession(String jwtId) {
        try {
            SessionObject session = null;

            // 優先使用 RMapCache，否則使用 RedisDataAccessor
            if (sessionCache != null) {
                session = sessionCache.get(jwtId);
            } else {
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);
                Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                session = sessionOpt.orElse(null);
            }

            if (session != null) {
                // 檢查 Session 是否已過期 (業務邏輯過期，RMapCache 已處理 TTL 過期)
                if (session.isExpired()) {
                    log.debug("Session expired by business logic, removing: jwtId={}", jwtId);
                    deleteSession(jwtId);
                    return Optional.empty();
                }

                // 自動續期檢查
                if (shouldAutoRenew(jwtId)) {
                    renewSession(jwtId);
                }

                log.debug("Session loaded successfully: jwtId={}, userId={}",
                        jwtId, session.getUserId());
                return Optional.of(session);
            }

            log.debug("Session not found: jwtId={}", jwtId);
            return Optional.empty();
        } catch (Exception e) {
            log.error("Failed to load session: jwtId={}", jwtId, e);
            throw new RuntimeException("Failed to load session", e);
        }
    }

    // ==================== Redisson 優化輔助方法 ====================
    /**
     * 使用 Redisson 更新索引
     */
    private void updateIndexesWithRedisson(String jwtId, SessionObject session, long ttlSeconds) {
        try {
            if (session.getUserId() != null && userIdIndex != null) {
                userIdIndex.put(session.getUserId(), jwtId);
                // RMultimap 不支援單個 key 的 expire，需要設定整個 multimap 的過期時間
                userIdIndex.expire(ttlSeconds, TimeUnit.SECONDS);
            }

            if (session.getClientId() != null && clientIdIndex != null) {
                clientIdIndex.put(session.getClientId(), jwtId);
                // RMultimap 不支援單個 key 的 expire，需要設定整個 multimap 的過期時間
                clientIdIndex.expire(ttlSeconds, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.warn("Failed to update indexes with Redisson for jwtId: {}", jwtId, e);
        }
    }

    /**
     * 清理過期 Session 的索引
     */
    private void cleanupIndexesForExpiredSession(String jwtId, SessionObject session) {
        try {
            if (session.getUserId() != null && userIdIndex != null) {
                userIdIndex.remove(session.getUserId(), jwtId);
            }

            if (session.getClientId() != null && clientIdIndex != null) {
                clientIdIndex.remove(session.getClientId(), jwtId);
            }

            // 更新統計計數器
            activeSessionsCounter.decrementAndGet();

            log.debug("Cleaned up indexes for expired session: jwtId={}", jwtId);
        } catch (Exception e) {
            log.warn("Failed to cleanup indexes for expired session: jwtId={}", jwtId, e);
        }
    }

    /**
     * 優化版：根據 userId 查找 Session
     */
    public List<SessionObject> findSessionsByUserIdOptimized(String userId) {
        if (userIdIndex != null) {
            try {
                return userIdIndex.get(userId).stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("Failed to find sessions by userId with Redisson: {}", userId, e);
            }
        }

        // 回退到傳統方式
        return findSessionsByUserId(userId);
    }

    /**
     * 優化版：根據 clientId 查找 Session
     */
    public List<SessionObject> findSessionsByClientIdOptimized(String clientId) {
        if (clientIdIndex != null) {
            try {
                return clientIdIndex.get(clientId).stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            } catch (Exception e) {
                log.error("Failed to find sessions by clientId with Redisson: {}", clientId, e);
            }
        }

        // 回退到傳統方式
        return findSessionsByClientId(clientId);
    }

    // ==================== 原有方法 ====================
    @Override
    public boolean deleteSession(String jwtId) {
        try {
            SessionObject session = null;
            boolean deleted = false;

            if (sessionCache != null) {
                // 使用 RMapCache
                session = sessionCache.get(jwtId);
                if (session != null) {
                    sessionCache.remove(jwtId);
                    deleted = true;

                    // 清理索引
                    cleanupIndexesForExpiredSession(jwtId, session);

                    // 更新統計計數器
                    activeSessionsCounter.decrementAndGet();
                }
            } else {
                // 回退到傳統方式
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);

                // 先載入 Session 以獲取索引資訊
                Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                session = sessionOpt.orElse(null);

                // 刪除主要 Session 資料
                deleted = redisDataAccessor.deleteObject(key);

                // 刪除索引
                if (session != null) {
                    removeSessionIndexes(jwtId, session);
                }
            }

            if (deleted) {
                log.debug("Session deleted successfully: jwtId={}", jwtId);
            } else {
                log.debug("Session not found for deletion: jwtId={}", jwtId);
            }

            return deleted;
        } catch (Exception e) {
            log.error("Failed to delete session: jwtId={}", jwtId, e);
            throw new RuntimeException("Failed to delete session", e);
        }
    }

    @Override
    public boolean existsSession(String jwtId) {
        try {
            if (sessionCache != null) {
                // 使用 RMapCache 檢查
                return sessionCache.containsKey(jwtId);
            } else {
                // 回退到傳統方式
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);
                return redisDataAccessor.exists(key);
            }
        } catch (Exception e) {
            log.error("Failed to check session existence: jwtId={}", jwtId, e);
            return false;
        }
    }

    @Override
    public boolean updateLastActiveTime(String jwtId) {
        try {
            Optional<SessionObject> sessionOpt = loadSession(jwtId);
            if (sessionOpt.isPresent()) {
                SessionObject session = sessionOpt.get();
                session.updateLastActiveTime();

                // 保持原有的 TTL
                long remainingTtl = getSessionRemainingTimeToLive(jwtId, TimeUnit.SECONDS);
                if (remainingTtl > 0) {
                    saveSession(jwtId, session, remainingTtl);
                    return true;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("Failed to update last active time: jwtId={}", jwtId, e);
            return false;
        }
    }

    @Override
    public boolean renewSession(String jwtId, long ttlSeconds) {
        try {
            boolean renewed = false;

            if (sessionCache != null) {
                // 使用 RMapCache 續期
                SessionObject session = sessionCache.get(jwtId);
                if (session != null) {
                    sessionCache.put(jwtId, session, ttlSeconds, TimeUnit.SECONDS);
                    renewed = true;

                    // 更新索引的 TTL
                    updateIndexesWithRedisson(jwtId, session, ttlSeconds);
                }
            } else {
                // 回退到傳統方式
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);
                renewed = redisDataAccessor.expire(key, ttlSeconds, TimeUnit.SECONDS);

                if (renewed) {
                    // 同時更新索引的 TTL
                    Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                    if (sessionOpt.isPresent()) {
                        renewSessionIndexes(jwtId, sessionOpt.get(), ttlSeconds);
                    }
                }
            }

            if (renewed) {
                log.debug("Session renewed successfully: jwtId={}, ttl={}s", jwtId, ttlSeconds);
            }

            return renewed;
        } catch (Exception e) {
            log.error("Failed to renew session: jwtId={}", jwtId, e);
            return false;
        }
    }

    @Override
    public boolean renewSession(String jwtId) {
        long defaultTtl = cacheProperties.getSession().getDefaultTimeToLive();
        return renewSession(jwtId, defaultTtl);
    }

    @Override
    public long getSessionRemainingTimeToLive(String jwtId, TimeUnit timeUnit) {
        try {
            if (sessionCache != null) {
                // 使用 RMapCache 獲取剩餘存活時間
                long remainingTimeMillis = sessionCache.remainTimeToLive(jwtId);
                if (remainingTimeMillis == -1) {
                    return -1; // 永不過期
                }
                if (remainingTimeMillis == -2) {
                    return -2; // Session 不存在
                }
                return timeUnit.convert(remainingTimeMillis, TimeUnit.MILLISECONDS);
            } else {
                // 回退到傳統方式
                String key = RedisKeyHelper.buildSessionKeyByJwtId(jwtId);
                return redisDataAccessor.getExpire(key, timeUnit);
            }
        } catch (Exception e) {
            log.error("Failed to get session remaining time to live: jwtId={}", jwtId, e);
            return -2; // 表示錯誤
        }
    }

    @Override
    public List<SessionObject> findSessionsByUserId(String userId) {
        try {
            if (userIdIndex != null) {
                // 使用 Redisson RMultimap 索引
                Collection<String> jwtIds = userIdIndex.get(userId);
                return jwtIds.stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            } else {
                // 回退到傳統方式
                String indexKey = RedisKeyHelper.buildKey("session_index", "user", userId);
                Set<String> jwtIds = redisDataAccessor.getSetMembers(indexKey, String.class);

                return jwtIds.stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("Failed to find sessions by userId: {}", userId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SessionObject> findSessionsByClientId(String clientId) {
        try {
            if (clientIdIndex != null) {
                // 使用 Redisson RMultimap 索引
                Collection<String> jwtIds = clientIdIndex.get(clientId);
                return jwtIds.stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            } else {
                // 回退到傳統方式
                String indexKey = RedisKeyHelper.buildKey("session_index", "client", clientId);
                Set<String> jwtIds = redisDataAccessor.getSetMembers(indexKey, String.class);

                return jwtIds.stream()
                        .map(this::loadSession)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("Failed to find sessions by clientId: {}", clientId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public long deleteSessionsByUserId(String userId) {
        try {
            List<SessionObject> sessions = findSessionsByUserId(userId);
            long deletedCount = 0;

            for (SessionObject session : sessions) {
                if (deleteSession(session.getJwtId())) {
                    deletedCount++;
                }
            }

            log.debug("Deleted {} sessions for userId: {}", deletedCount, userId);
            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to delete sessions by userId: {}", userId, e);
            return 0;
        }
    }

    @Override
    public long deleteSessionsByClientId(String clientId) {
        try {
            List<SessionObject> sessions = findSessionsByClientId(clientId);
            long deletedCount = 0;

            for (SessionObject session : sessions) {
                if (deleteSession(session.getJwtId())) {
                    deletedCount++;
                }
            }

            log.debug("Deleted {} sessions for clientId: {}", deletedCount, clientId);
            return deletedCount;
        } catch (Exception e) {
            log.error("Failed to delete sessions by clientId: {}", clientId, e);
            return 0;
        }
    }

    @Override
    public long cleanupExpiredSessions() {
        try {
            // 使用模式查找所有 session keys
            String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
            Set<String> sessionKeys = redisDataAccessor.keys(pattern);

            long cleanedCount = 0;
            for (String key : sessionKeys) {
                try {
                    Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                    if (sessionOpt.isPresent() && sessionOpt.get().isExpired()) {
                        String jwtId = extractJwtIdFromKey(key);
                        if (deleteSession(jwtId)) {
                            cleanedCount++;
                        }
                    }
                } catch (Exception e) {
                    log.warn("Failed to check session expiry for key: {}", key, e);
                }
            }

            log.debug("Cleaned up {} expired sessions", cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("Failed to cleanup expired sessions", e);
            return 0;
        }
    }

    @Override
    public long getActiveSessionCount() {
        try {
            // 優先使用 RMapCache 的高效能方法
            if (sessionCache != null) {
                return sessionCache.size();
            }

            // 使用計數器 (如果可用)
            long counterValue = activeSessionsCounter.get();
            if (counterValue > 0) {
                return counterValue;
            }

            // 回退到傳統方式 (效能較差，但保證準確性)
            log.warn("Using slow keys() operation for session count - consider using RMapCache optimization");
            String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
            Set<String> sessionKeys = redisDataAccessor.keys(pattern);

            long activeCount = 0;
            for (String key : sessionKeys) {
                try {
                    Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                    if (sessionOpt.isPresent() && sessionOpt.get().isActive()) {
                        activeCount++;
                    }
                } catch (Exception e) {
                    log.warn("Failed to check session status for key: {}", key, e);
                }
            }

            return activeCount;
        } catch (Exception e) {
            log.error("Failed to get active session count", e);
            return 0;
        }
    }

    @Override
    public SessionStats getSessionStats() {
        return new SessionStatsImpl();
    }

    // ==================== 私有輔助方法 ====================
    /**
     * 建立 Session 索引
     */
    private void createSessionIndexes(String jwtId, SessionObject session, long ttlSeconds) {
        try {
            // 建立 userId 索引
            if (session.getUserId() != null) {
                String userIndexKey = RedisKeyHelper.buildKey("session_index", "user", session.getUserId());
                redisDataAccessor.addToSet(userIndexKey, jwtId);
                redisDataAccessor.expire(userIndexKey, ttlSeconds, TimeUnit.SECONDS);
            }

            // 建立 clientId 索引
            if (session.getClientId() != null) {
                String clientIndexKey = RedisKeyHelper.buildKey("session_index", "client", session.getClientId());
                redisDataAccessor.addToSet(clientIndexKey, jwtId);
                redisDataAccessor.expire(clientIndexKey, ttlSeconds, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.warn("Failed to create session indexes for jwtId: {}", jwtId, e);
        }
    }

    /**
     * 移除 Session 索引
     */
    private void removeSessionIndexes(String jwtId, SessionObject session) {
        try {
            // 移除 userId 索引
            if (session.getUserId() != null) {
                String userIndexKey = RedisKeyHelper.buildKey("session_index", "user", session.getUserId());
                redisDataAccessor.removeFromSet(userIndexKey, jwtId);
            }

            // 移除 clientId 索引
            if (session.getClientId() != null) {
                String clientIndexKey = RedisKeyHelper.buildKey("session_index", "client", session.getClientId());
                redisDataAccessor.removeFromSet(clientIndexKey, jwtId);
            }
        } catch (Exception e) {
            log.warn("Failed to remove session indexes for jwtId: {}", jwtId, e);
        }
    }

    /**
     * 續期 Session 索引
     */
    private void renewSessionIndexes(String jwtId, SessionObject session, long ttlSeconds) {
        try {
            // 續期 userId 索引
            if (session.getUserId() != null) {
                String userIndexKey = RedisKeyHelper.buildKey("session_index", "user", session.getUserId());
                redisDataAccessor.expire(userIndexKey, ttlSeconds, TimeUnit.SECONDS);
            }

            // 續期 clientId 索引
            if (session.getClientId() != null) {
                String clientIndexKey = RedisKeyHelper.buildKey("session_index", "client", session.getClientId());
                redisDataAccessor.expire(clientIndexKey, ttlSeconds, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.warn("Failed to renew session indexes for jwtId: {}", jwtId, e);
        }
    }

    /**
     * 檢查是否應該自動續期
     */
    private boolean shouldAutoRenew(String jwtId) {
        if (!cacheProperties.getSession().isAutoRenewal()) {
            return false;
        }

        try {
            long remainingTtl = getSessionRemainingTimeToLive(jwtId, TimeUnit.SECONDS);
            long defaultTtl = cacheProperties.getSession().getDefaultTimeToLive();
            double threshold = cacheProperties.getSession().getRenewalThreshold();

            return remainingTtl > 0 && remainingTtl < (defaultTtl * threshold);
        } catch (Exception e) {
            log.warn("Failed to check auto renewal for jwtId: {}", jwtId, e);
            return false;
        }
    }

    /**
     * 從 Redis key 中提取 JWT ID
     */
    private String extractJwtIdFromKey(String key) {
        // key 格式: session:jwt:{jwtId}
        String[] parts = key.split(":");
        return parts.length >= 3 ? parts[2] : null;
    }

    /**
     * Session 統計資訊實現
     */
    private class SessionStatsImpl implements SessionStats {

        @Override
        public long getTotalSessions() {
            try {
                if (sessionCache != null) {
                    // 使用 RMapCache 獲取總數
                    return sessionCache.size();
                } else {
                    // 回退到傳統方式
                    String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
                    return redisDataAccessor.keys(pattern).size();
                }
            } catch (Exception e) {
                log.error("Failed to get total sessions count", e);
                return 0;
            }
        }

        @Override
        public long getActiveSessions() {
            return getActiveSessionCount();
        }

        @Override
        public long getExpiredSessions() {
            try {
                String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
                Set<String> sessionKeys = redisDataAccessor.keys(pattern);

                long expiredCount = 0;
                for (String key : sessionKeys) {
                    try {
                        Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                        if (sessionOpt.isPresent() && sessionOpt.get().isExpired()) {
                            expiredCount++;
                        }
                    } catch (Exception e) {
                        log.warn("Failed to check session expiry for key: {}", key, e);
                    }
                }

                return expiredCount;
            } catch (Exception e) {
                log.error("Failed to get expired sessions count", e);
                return 0;
            }
        }

        @Override
        public long getTodayNewSessions() {
            try {
                String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
                Set<String> sessionKeys = redisDataAccessor.keys(pattern);

                LocalDateTime todayStart = LocalDateTime.now().toLocalDate().atStartOfDay();
                long todayCount = 0;

                for (String key : sessionKeys) {
                    try {
                        Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                        if (sessionOpt.isPresent()) {
                            SessionObject session = sessionOpt.get();
                            if (session.getCreatedAt() != null
                                    && session.getCreatedAt().isAfter(todayStart)) {
                                todayCount++;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Failed to check session creation time for key: {}", key, e);
                    }
                }

                return todayCount;
            } catch (Exception e) {
                log.error("Failed to get today's new sessions count", e);
                return 0;
            }
        }

        @Override
        public double getAverageSessionDuration() {
            try {
                String pattern = RedisKeyHelper.buildKey("session", "jwt", "*");
                Set<String> sessionKeys = redisDataAccessor.keys(pattern);

                if (sessionKeys.isEmpty()) {
                    return 0.0;
                }

                long totalDuration = 0;
                int validSessions = 0;

                for (String key : sessionKeys) {
                    try {
                        Optional<SessionObject> sessionOpt = redisDataAccessor.getObject(key, SessionObject.class);
                        if (sessionOpt.isPresent()) {
                            SessionObject session = sessionOpt.get();
                            if (session.getCreatedAt() != null && session.getLastActiveTime() != null) {
                                long duration = java.time.Duration.between(
                                        session.getCreatedAt(), session.getLastActiveTime()).getSeconds();
                                totalDuration += duration;
                                validSessions++;
                            }
                        }
                    } catch (Exception e) {
                        log.warn("Failed to calculate session duration for key: {}", key, e);
                    }
                }

                return validSessions > 0 ? (double) totalDuration / validSessions : 0.0;
            } catch (Exception e) {
                log.error("Failed to calculate average session duration", e);
                return 0.0;
            }
        }
    }
}
