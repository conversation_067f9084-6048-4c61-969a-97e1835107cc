package com.nanshan.common.cache.util;

import org.springframework.util.StringUtils;

import lombok.experimental.UtilityClass;

/**
 * Redis Key 命名規則建構工具
 *
 * 統一管理 Redis key 的命名規則，確保 key 的一致性和可讀性
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@UtilityClass
public class RedisKeyHelper {
    
    // Key 分隔符
    private static final String SEPARATOR = ":";
    
    // Key 前綴
    private static final String SESSION_PREFIX = "session";
    private static final String CACHE_PREFIX = "cache";
    private static final String LOCK_PREFIX = "lock";
    
    /**
     * 建構 Session Key
     * 格式: session:{clientId}:{userId}
     * 
     * @param clientId 客戶端 ID
     * @param userId 使用者 ID
     * @return Session Key
     */
    public static String buildSessionKey(String clientId, String userId) {
        validateNotEmpty(clientId, "clientId");
        validateNotEmpty(userId, "userId");
        return buildKey(SESSION_PREFIX, clientId, userId);
    }
    
    /**
     * 建構 Session Key (使用 JWT ID)
     * 格式: session:jwt:{jwtId}
     * 
     * @param jwtId JWT ID
     * @return Session Key
     */
    public static String buildSessionKeyByJwtId(String jwtId) {
        validateNotEmpty(jwtId, "jwtId");
        return buildKey(SESSION_PREFIX, "jwt", jwtId);
    }
    
    /**
     * 建構 Cache Key
     * 格式: cache:{type}:{id}
     * 
     * @param type 快取類型
     * @param id 快取 ID
     * @return Cache Key
     */
    public static String buildCacheKey(String type, String id) {
        validateNotEmpty(type, "type");
        validateNotEmpty(id, "id");
        return buildKey(CACHE_PREFIX, type, id);
    }
    
    /**
     * 建構 Cache Key (多層級)
     * 格式: cache:{type}:{subType}:{id}
     * 
     * @param type 快取類型
     * @param subType 快取子類型
     * @param id 快取 ID
     * @return Cache Key
     */
    public static String buildCacheKey(String type, String subType, String id) {
        validateNotEmpty(type, "type");
        validateNotEmpty(subType, "subType");
        validateNotEmpty(id, "id");
        return buildKey(CACHE_PREFIX, type, subType, id);
    }
    
    /**
     * 建構 Lock Key
     * 格式: lock:{resource}
     * 
     * @param resource 資源名稱
     * @return Lock Key
     */
    public static String buildLockKey(String resource) {
        validateNotEmpty(resource, "resource");
        return buildKey(LOCK_PREFIX, resource);
    }
    
    /**
     * 建構 Lock Key (多層級)
     * 格式: lock:{type}:{resource}
     * 
     * @param type 鎖類型
     * @param resource 資源名稱
     * @return Lock Key
     */
    public static String buildLockKey(String type, String resource) {
        validateNotEmpty(type, "type");
        validateNotEmpty(resource, "resource");
        return buildKey(LOCK_PREFIX, type, resource);
    }
    
    /**
     * 建構通用 Key
     * 
     * @param parts Key 組成部分
     * @return 完整的 Key
     */
    public static String buildKey(String... parts) {
        if (parts == null || parts.length == 0) {
            throw new IllegalArgumentException("Key parts cannot be null or empty");
        }
        
        for (String part : parts) {
            validateNotEmpty(part, "key part");
        }
        
        return String.join(SEPARATOR, parts);
    }
    
    /**
     * 建構 Pattern Key (用於批量查詢)
     * 
     * @param prefix 前綴
     * @param pattern 模式 (使用 * 作為萬用字元)
     * @return Pattern Key
     */
    public static String buildPatternKey(String prefix, String pattern) {
        validateNotEmpty(prefix, "prefix");
        validateNotEmpty(pattern, "pattern");
        return prefix + SEPARATOR + pattern;
    }
    
    /**
     * 從 Key 中提取前綴
     * 
     * @param key Redis Key
     * @return 前綴
     */
    public static String extractPrefix(String key) {
        validateNotEmpty(key, "key");
        int separatorIndex = key.indexOf(SEPARATOR);
        if (separatorIndex == -1) {
            return key;
        }
        return key.substring(0, separatorIndex);
    }
    
    /**
     * 從 Key 中提取指定位置的部分
     * 
     * @param key Redis Key
     * @param index 位置索引 (從 0 開始)
     * @return Key 的指定部分
     */
    public static String extractPart(String key, int index) {
        validateNotEmpty(key, "key");
        String[] parts = key.split(SEPARATOR);
        if (index < 0 || index >= parts.length) {
            throw new IllegalArgumentException("Index out of bounds: " + index);
        }
        return parts[index];
    }
    
    /**
     * 檢查 Key 是否為 Session Key
     * 
     * @param key Redis Key
     * @return 是否為 Session Key
     */
    public static boolean isSessionKey(String key) {
        return key != null && key.startsWith(SESSION_PREFIX + SEPARATOR);
    }
    
    /**
     * 檢查 Key 是否為 Cache Key
     * 
     * @param key Redis Key
     * @return 是否為 Cache Key
     */
    public static boolean isCacheKey(String key) {
        return key != null && key.startsWith(CACHE_PREFIX + SEPARATOR);
    }
    
    /**
     * 檢查 Key 是否為 Lock Key
     * 
     * @param key Redis Key
     * @return 是否為 Lock Key
     */
    public static boolean isLockKey(String key) {
        return key != null && key.startsWith(LOCK_PREFIX + SEPARATOR);
    }
    
    /**
     * 驗證字串不為空
     * 
     * @param value 要驗證的值
     * @param fieldName 欄位名稱
     */
    private static void validateNotEmpty(String value, String fieldName) {
        if (!StringUtils.hasText(value)) {
            throw new IllegalArgumentException(fieldName + " cannot be null or empty");
        }
    }
}
