package com.nanshan.common.cache.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.junit.jupiter.api.Test;

import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.*;

/**
 * JSON 解析測試
 *
 * 驗證強化的 ObjectMapper 能夠處理控制字符
 *
 * <AUTHOR>
 * @since 1.0.0
 */
class JsonParsingTest {

    /**
     * 測試創建強化的 ObjectMapper
     */
    @Test
    void testCreateRobustObjectMapper() {
        ObjectMapper objectMapper = createRobustObjectMapper();

        assertNotNull(objectMapper);

        // 驗證 Java 8 時間模組已註冊
        assertTrue(objectMapper.getRegisteredModuleIds().contains("jackson-datatype-jsr310"));

        // 驗證控制字符處理已啟用
        assertTrue(objectMapper.isEnabled(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS));
        assertTrue(objectMapper.isEnabled(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER));

        // 驗證容錯性配置
        assertFalse(objectMapper.isEnabled(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES));
        assertTrue(objectMapper.isEnabled(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT));

        // 驗證序列化配置
        assertFalse(objectMapper.isEnabled(SerializationFeature.FAIL_ON_EMPTY_BEANS));
        assertFalse(objectMapper.isEnabled(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS));
    }

    /**
     * 測試解析包含控制字符的 JSON
     */
    @Test
    void testParseJsonWithControlCharacters() throws Exception {
        ObjectMapper objectMapper = createRobustObjectMapper();

        // 包含控制字符的 JSON 字符串（ASCII code 3）
        String jsonWithControlChar = "{\"message\":\"Hello\u0003World\"}";

        // 應該能夠成功解析
        assertDoesNotThrow(() -> {
            var result = objectMapper.readValue(jsonWithControlChar, Object.class);
            assertNotNull(result);
        });
    }

    /**
     * 測試解析包含反斜線轉義的 JSON
     */
    @Test
    void testParseJsonWithBackslashEscaping() throws Exception {
        ObjectMapper objectMapper = createRobustObjectMapper();

        // 包含特殊轉義字符的 JSON
        String jsonWithEscaping = "{\"path\":\"C:\\\\Users\\\\<USER>\"}";

        // 應該能夠成功解析
        assertDoesNotThrow(() -> {
            var result = objectMapper.readValue(jsonWithEscaping, Object.class);
            assertNotNull(result);
        });
    }

    /**
     * 測試解析空字符串為 null
     */
    @Test
    void testParseEmptyStringAsNull() throws Exception {
        ObjectMapper objectMapper = createRobustObjectMapper();

        // 包含空字符串的 JSON
        String jsonWithEmptyString = "{\"value\":\"\"}";

        // 應該能夠成功解析，空字符串被轉換為 null
        assertDoesNotThrow(() -> {
            var result = objectMapper.readValue(jsonWithEmptyString, Object.class);
            assertNotNull(result);
        });
    }

    /**
     * 創建強化的 ObjectMapper（複製自 RedissonConfig）
     */
    private ObjectMapper createRobustObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 支援 Java 8 時間 API
        objectMapper.registerModule(new JavaTimeModule());

        // 配置 JSON 解析特性以處理控制字符 - 這是解決問題的關鍵
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

        // 配置反序列化特性以提高容錯性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 配置序列化特性
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // 設置時區
        objectMapper.setTimeZone(TimeZone.getDefault());

        return objectMapper;
    }
}
