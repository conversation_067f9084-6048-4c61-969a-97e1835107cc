package com.nanshan.common.cache.model;

import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SessionObject 測試
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class SessionObjectTest {
    
    @Test
    void testSessionObjectBuilder() {
        SessionObject session = SessionObject.builder()
                .userId("user123")
                .clientId("portal")
                .jwtId("jwt123")
                .loginTime(LocalDateTime.now())
                .status(SessionObject.SessionStatus.ACTIVE)
                .build();
        
        assertEquals("user123", session.getUserId());
        assertEquals("portal", session.getClientId());
        assertEquals("jwt123", session.getJwtId());
        assertEquals(SessionObject.SessionStatus.ACTIVE, session.getStatus());
    }
    
    @Test
    void testIsExpired() {
        SessionObject session = new SessionObject();
        
        // 沒有設定過期時間，不應該過期
        assertFalse(session.isExpired());
        
        // 設定未來的過期時間，不應該過期
        session.setExpireTime(LocalDateTime.now().plusMinutes(10));
        assertFalse(session.isExpired());
        
        // 設定過去的過期時間，應該過期
        session.setExpireTime(LocalDateTime.now().minusMinutes(10));
        assertTrue(session.isExpired());
    }
    
    @Test
    void testIsActive() {
        SessionObject session = new SessionObject();
        session.setStatus(SessionObject.SessionStatus.ACTIVE);
        
        // 活躍狀態且未過期
        assertTrue(session.isActive());
        
        // 設定過期時間為過去，應該不活躍
        session.setExpireTime(LocalDateTime.now().minusMinutes(10));
        assertFalse(session.isActive());
        
        // 設定狀態為過期，應該不活躍
        session.setExpireTime(LocalDateTime.now().plusMinutes(10));
        session.setStatus(SessionObject.SessionStatus.EXPIRED);
        assertFalse(session.isActive());
    }
    
    @Test
    void testUpdateLastActiveTime() {
        SessionObject session = new SessionObject();
        LocalDateTime beforeUpdate = LocalDateTime.now();
        
        session.updateLastActiveTime();
        
        assertNotNull(session.getLastActiveTime());
        assertNotNull(session.getUpdatedAt());
        assertTrue(session.getLastActiveTime().isAfter(beforeUpdate) || 
                  session.getLastActiveTime().isEqual(beforeUpdate));
        assertTrue(session.getUpdatedAt().isAfter(beforeUpdate) || 
                  session.getUpdatedAt().isEqual(beforeUpdate));
    }
    
    @Test
    void testExpire() {
        SessionObject session = new SessionObject();
        session.setStatus(SessionObject.SessionStatus.ACTIVE);
        
        session.expire();
        
        assertEquals(SessionObject.SessionStatus.EXPIRED, session.getStatus());
        assertNotNull(session.getUpdatedAt());
    }
    
    @Test
    void testLogout() {
        SessionObject session = new SessionObject();
        session.setStatus(SessionObject.SessionStatus.ACTIVE);
        
        session.logout();
        
        assertEquals(SessionObject.SessionStatus.LOGGED_OUT, session.getStatus());
        assertNotNull(session.getUpdatedAt());
    }
    
    @Test
    void testSessionObjectBuilderHelper() {
        // 測試基本 Session 建立
        SessionObject basicSession = SessionObjectBuilder.createBasicSession(
            "user123", "portal", "jwt123");
        
        assertEquals("user123", basicSession.getUserId());
        assertEquals("portal", basicSession.getClientId());
        assertEquals("jwt123", basicSession.getJwtId());
        assertEquals(SessionObject.SessionStatus.ACTIVE, basicSession.getStatus());
        assertNotNull(basicSession.getCreatedAt());
        assertNotNull(basicSession.getUpdatedAt());
        assertNotNull(basicSession.getLastActiveTime());
        assertNotNull(basicSession.getAttributes());
    }
    
    @Test
    void testSessionObjectBuilderFullSession() {
        // 測試完整 Session 建立
        SessionObject fullSession = SessionObjectBuilder.createFullSession(
            "user123", "portal", "jwt123",
            Arrays.asList("ADMIN", "USER"),
            Arrays.asList("READ", "WRITE"),
            "***********",
            "Chrome/Windows",
            "Mozilla/5.0...",
            3600
        );
        
        assertEquals("user123", fullSession.getUserId());
        assertEquals("portal", fullSession.getClientId());
        assertEquals("jwt123", fullSession.getJwtId());
        assertEquals(Arrays.asList("ADMIN", "USER"), fullSession.getRoles());
        assertEquals(Arrays.asList("READ", "WRITE"), fullSession.getPermissions());
        assertEquals("***********", fullSession.getIpAddress());
        assertEquals("Chrome/Windows", fullSession.getDeviceInfo());
        assertEquals("Mozilla/5.0...", fullSession.getUserAgent());
        assertNotNull(fullSession.getExpireTime());
        assertEquals(SessionObject.SessionStatus.ACTIVE, fullSession.getStatus());
    }
    
    @Test
    void testSessionObjectBuilderCopyFrom() {
        // 建立原始 Session
        SessionObject original = SessionObjectBuilder.createBasicSession(
            "user123", "portal", "jwt123");
        original.setRoles(Arrays.asList("USER"));
        
        Map<String, Object> attributes = new HashMap<>();
        attributes.put("key1", "value1");
        original.setAttributes(attributes);
        
        // 複製 Session
        SessionObject copied = SessionObjectBuilder.copyFrom(original);
        
        assertEquals(original.getUserId(), copied.getUserId());
        assertEquals(original.getClientId(), copied.getClientId());
        assertEquals(original.getJwtId(), copied.getJwtId());
        assertEquals(original.getRoles(), copied.getRoles());
        assertEquals(original.getAttributes(), copied.getAttributes());
        
        // 確保是深拷貝
        assertNotSame(original.getAttributes(), copied.getAttributes());
        
        // 測試 null 情況
        assertNull(SessionObjectBuilder.copyFrom(null));
    }
    
    @Test
    void testSessionObjectBuilderUpdateExpireTime() {
        SessionObject session = SessionObjectBuilder.createBasicSession(
            "user123", "portal", "jwt123");
        
        SessionObject updated = SessionObjectBuilder.updateExpireTime(session, 7200);
        
        assertNotNull(updated.getExpireTime());
        assertTrue(updated.getExpireTime().isAfter(LocalDateTime.now().plusSeconds(7100)));
        assertTrue(updated.getExpireTime().isBefore(LocalDateTime.now().plusSeconds(7300)));
        
        // 測試 null 情況
        assertNull(SessionObjectBuilder.updateExpireTime(null, 3600));
    }
}
