package com.nanshan.common.cache.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RedisKeyHelper 測試
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
class RedisKeyHelperTest {
    
    @Test
    void testBuildSessionKey() {
        String key = RedisKeyHelper.buildSessionKey("portal", "user123");
        assertEquals("session:portal:user123", key);
    }
    
    @Test
    void testBuildSessionKeyByJwtId() {
        String key = RedisKeyHelper.buildSessionKeyByJwtId("jwt123");
        assertEquals("session:jwt:jwt123", key);
    }
    
    @Test
    void testBuildCacheKey() {
        String key = RedisKeyHelper.buildCacheKey("user", "123");
        assertEquals("cache:user:123", key);
    }
    
    @Test
    void testBuildCacheKeyWithSubType() {
        String key = RedisKeyHelper.buildCacheKey("user", "profile", "123");
        assertEquals("cache:user:profile:123", key);
    }
    
    @Test
    void testBuildLockKey() {
        String key = RedisKeyHelper.buildLockKey("order");
        assertEquals("lock:order", key);
    }
    
    @Test
    void testBuildLockKeyWithType() {
        String key = RedisKeyHelper.buildLockKey("fair", "order");
        assertEquals("lock:fair:order", key);
    }
    
    @Test
    void testBuildKey() {
        String key = RedisKeyHelper.buildKey("prefix", "middle", "suffix");
        assertEquals("prefix:middle:suffix", key);
    }
    
    @Test
    void testBuildPatternKey() {
        String key = RedisKeyHelper.buildPatternKey("session", "*");
        assertEquals("session:*", key);
    }
    
    @Test
    void testExtractPrefix() {
        String prefix = RedisKeyHelper.extractPrefix("session:portal:user123");
        assertEquals("session", prefix);
    }
    
    @Test
    void testExtractPart() {
        String part = RedisKeyHelper.extractPart("session:portal:user123", 1);
        assertEquals("portal", part);
        
        part = RedisKeyHelper.extractPart("session:portal:user123", 2);
        assertEquals("user123", part);
    }
    
    @Test
    void testIsSessionKey() {
        assertTrue(RedisKeyHelper.isSessionKey("session:portal:user123"));
        assertFalse(RedisKeyHelper.isSessionKey("cache:user:123"));
        assertFalse(RedisKeyHelper.isSessionKey("lock:order"));
    }
    
    @Test
    void testIsCacheKey() {
        assertTrue(RedisKeyHelper.isCacheKey("cache:user:123"));
        assertFalse(RedisKeyHelper.isCacheKey("session:portal:user123"));
        assertFalse(RedisKeyHelper.isCacheKey("lock:order"));
    }
    
    @Test
    void testIsLockKey() {
        assertTrue(RedisKeyHelper.isLockKey("lock:order"));
        assertFalse(RedisKeyHelper.isLockKey("session:portal:user123"));
        assertFalse(RedisKeyHelper.isLockKey("cache:user:123"));
    }
    
    @Test
    void testValidateNotEmpty() {
        // 測試正常情況
        assertDoesNotThrow(() -> RedisKeyHelper.buildKey("valid", "key"));
        
        // 測試空值情況
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.buildKey(null, "key"));
        
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.buildKey("", "key"));
        
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.buildKey("key", null));
        
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.buildKey("key", ""));
    }
    
    @Test
    void testExtractPartOutOfBounds() {
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.extractPart("session:portal:user123", 5));
        
        assertThrows(IllegalArgumentException.class, () -> 
            RedisKeyHelper.extractPart("session:portal:user123", -1));
    }
}
