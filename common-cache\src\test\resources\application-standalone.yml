# 獨立環境測試配置
# 適用於各種獨立部署環境的測試配置

spring:
  profiles:
    active: standalone
  data:
    redis:
      # 支援環境變數配置，適應不同的獨立環境
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:15}  # 使用高編號資料庫避免與生產環境衝突
      username: ${REDIS_USERNAME:}
      password: ${REDIS_PASSWORD:}
      timeout: ${REDIS_TIMEOUT:10000ms}  # 獨立環境可能網路延遲較高
      
      # 連接池配置 - 適合獨立環境
      lettuce:
        pool:
          max-active: ${REDIS_POOL_MAX_ACTIVE:8}  # 獨立環境較小的連接池
          max-idle: ${REDIS_POOL_MAX_IDLE:4}
          min-idle: ${REDIS_POOL_MIN_IDLE:1}
          max-wait: ${REDIS_POOL_MAX_WAIT:5000ms}
        shutdown-timeout: ${REDIS_SHUTDOWN_TIMEOUT:2000ms}

# Common Cache 獨立環境配置
common:
  cache:
    # Redis 連接配置 - 獨立環境優化
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      database: ${REDIS_DATABASE:15}
      username: ${REDIS_USERNAME:}
      password: ${REDIS_PASSWORD:}
      timeout: ${REDIS_TIMEOUT:10000}
      
      # 連接池配置 - 獨立環境
      connection-pool:
        pool-size: ${REDIS_POOL_SIZE:8}
        minimum-idle-size: ${REDIS_POOL_MIN_IDLE:1}
        idle-connection-timeout: ${REDIS_IDLE_TIMEOUT:15000}
        connection-timeout: ${REDIS_CONNECT_TIMEOUT:10000}
      
      # 重試配置 - 獨立環境可能不穩定
      retry:
        attempts: ${REDIS_RETRY_ATTEMPTS:5}
        interval: ${REDIS_RETRY_INTERVAL:2000}
      
      # 線程池配置
      thread-pool:
        threads: ${REDIS_THREADS:4}
        netty-threads: ${REDIS_NETTY_THREADS:8}
    
    # Session 管理配置 - 獨立環境
    session:
      enabled: true
      default-time-to-live: ${SESSION_TTL:600}  # 10分鐘，測試用較短時間
      key-prefix: ${SESSION_PREFIX:standalone:session}
      cleanup-interval: ${SESSION_CLEANUP_INTERVAL:120}  # 2分鐘清理一次
      auto-renewal: ${SESSION_AUTO_RENEWAL:true}
      renewal-threshold: ${SESSION_RENEWAL_THRESHOLD:0.3}
    
    # 快取管理配置 - 獨立環境
    cache:
      enabled: true
      default-time-to-live: ${CACHE_TTL:1200}  # 20分鐘
      key-prefix: ${CACHE_PREFIX:standalone:cache}
      auto-renewal: ${CACHE_AUTO_RENEWAL:true}
      renewal-threshold: ${CACHE_RENEWAL_THRESHOLD:0.2}
      max-size: ${CACHE_MAX_SIZE:500}  # 獨立環境較小的快取容量
    
    # 分散式鎖配置 - 獨立環境
    lock:
      enabled: true
      default-wait-time: ${LOCK_WAIT_TIME:15}  # 獨立環境可能需要更長等待時間
      default-lease-time: ${LOCK_LEASE_TIME:30}
      key-prefix: ${LOCK_PREFIX:standalone:lock}
    
    # 清理器配置 - 獨立環境
    cleaner:
      enabled: ${CLEANER_ENABLED:true}
      schedule-interval: ${CLEANER_INTERVAL:180}  # 3分鐘清理一次
      batch-size: ${CLEANER_BATCH_SIZE:50}  # 較小的批次大小
      expired-key-scan-count: ${CLEANER_SCAN_COUNT:25}
    
    # 環境保護配置 - 獨立環境
    env-guard:
      enabled: true
      dangerous-operations-enabled: ${ALLOW_DANGEROUS_OPS:true}  # 測試環境允許
      production-profiles: 
        - prod
        - production
        - staging

# 測試專用配置
test:
  # 環境檢查配置
  environment:
    check-redis-connection: ${TEST_CHECK_REDIS:true}
    check-pubsub-permissions: ${TEST_CHECK_PUBSUB:true}
    check-script-permissions: ${TEST_CHECK_SCRIPT:true}
    graceful-degradation: ${TEST_GRACEFUL_DEGRADATION:true}
  
  # 測試超時配置 - 獨立環境可能較慢
  timeouts:
    connection-test: ${TEST_CONNECTION_TIMEOUT:10000}
    operation-test: ${TEST_OPERATION_TIMEOUT:5000}
    lock-test: ${TEST_LOCK_TIMEOUT:15000}
    cleanup-test: ${TEST_CLEANUP_TIMEOUT:30000}
  
  # 測試重試配置
  retry:
    max-attempts: ${TEST_RETRY_ATTEMPTS:3}
    delay-between-attempts: ${TEST_RETRY_DELAY:2000}
    exponential-backoff: ${TEST_EXPONENTIAL_BACKOFF:true}

# 日誌配置 - 獨立環境測試
logging:
  level:
    '[com.nanshan.common.cache]': ${LOG_LEVEL_CACHE:INFO}
    '[org.redisson]': ${LOG_LEVEL_REDISSON:WARN}
    '[org.springframework.data.redis]': ${LOG_LEVEL_REDIS:WARN}
    '[org.springframework.test]': ${LOG_LEVEL_TEST:INFO}
    '[org.springframework.boot.test]': ${LOG_LEVEL_BOOT_TEST:INFO}
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - %msg%n"
  file:
    name: ${LOG_FILE:logs/standalone-test.log}

# Actuator 配置 - 測試監控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
      show-components: always
  health:
    redis:
      enabled: true
    defaults:
      enabled: true
