# 測試環境配置 - 支援獨立環境
spring:
  profiles:
    active: test
  data:
    redis:
      host: ${TEST_REDIS_HOST:localhost}
      port: ${TEST_REDIS_PORT:6379}
      database: ${TEST_REDIS_DATABASE:15}  # 使用高編號資料庫避免衝突
      username: ${TEST_REDIS_USERNAME:}
      password: ${TEST_REDIS_PASSWORD:}
      timeout: ${TEST_REDIS_TIMEOUT:5000ms}
      lettuce:
        pool:
          max-active: ${TEST_REDIS_POOL_MAX_ACTIVE:16}
          max-idle: ${TEST_REDIS_POOL_MAX_IDLE:8}
          min-idle: ${TEST_REDIS_POOL_MIN_IDLE:2}
          max-wait: ${TEST_REDIS_POOL_MAX_WAIT:3000ms}

# Common Cache 測試配置 - 獨立環境適配
common:
  cache:
    redis:
      host: ${TEST_REDIS_HOST:localhost}
      port: ${TEST_REDIS_PORT:6379}
      database: ${TEST_REDIS_DATABASE:15}
      username: ${TEST_REDIS_USERNAME:}
      password: ${TEST_REDIS_PASSWORD:}
      timeout: ${TEST_REDIS_TIMEOUT:5000}
      connection-pool:
        pool-size: ${TEST_REDIS_POOL_SIZE:16}
        minimum-idle-size: ${TEST_REDIS_POOL_MIN_IDLE:2}
        idle-connection-timeout: ${TEST_REDIS_IDLE_TIMEOUT:10000}
      retry:
        attempts: ${TEST_REDIS_RETRY_ATTEMPTS:3}
        interval: ${TEST_REDIS_RETRY_INTERVAL:1000}

    # 測試環境功能配置 - 適合獨立環境
    session:
      enabled: true
      default-time-to-live: ${TEST_SESSION_TTL:300}  # 測試環境較短 TTL
      key-prefix: ${TEST_SESSION_PREFIX:test:session}
      cleanup-interval: ${TEST_SESSION_CLEANUP:60}
      auto-renewal: true
      renewal-threshold: 0.5

    cache:
      enabled: true
      default-time-to-live: ${TEST_CACHE_TTL:600}  # 測試環境較短 TTL
      key-prefix: ${TEST_CACHE_PREFIX:test:cache}
      auto-renewal: true
      renewal-threshold: 0.5
      max-size: ${TEST_CACHE_MAX_SIZE:1000}

    lock:
      enabled: true
      default-wait-time: ${TEST_LOCK_WAIT_TIME:5}  # 測試環境較短等待時間
      default-lease-time: ${TEST_LOCK_LEASE_TIME:15}  # 測試環境較短租約時間
      key-prefix: ${TEST_LOCK_PREFIX:test:lock}

    cleaner:
      enabled: true
      schedule-interval: ${TEST_CLEANER_INTERVAL:60}  # 測試環境更頻繁清理
      batch-size: ${TEST_CLEANER_BATCH_SIZE:100}
      expired-key-scan-count: ${TEST_CLEANER_SCAN_COUNT:50}

    env-guard:
      enabled: true
      dangerous-operations-enabled: ${TEST_ALLOW_DANGEROUS_OPS:true}  # 測試環境允許危險操作
      production-profiles:
        - prod
        - production

# 日誌配置 - 測試環境
logging:
  level:
    '[com.nanshan.common.cache]': ${TEST_LOG_LEVEL_CACHE:DEBUG}
    '[org.redisson]': ${TEST_LOG_LEVEL_REDISSON:WARN}
    '[org.springframework.data.redis]': ${TEST_LOG_LEVEL_REDIS:WARN}
    '[org.springframework.test]': ${TEST_LOG_LEVEL_TEST:INFO}
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
