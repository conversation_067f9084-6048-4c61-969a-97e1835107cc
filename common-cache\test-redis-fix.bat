@echo off
echo ========================================
echo Redis PubSub 分散式鎖修復測試腳本
echo ========================================
echo.

set BASE_URL=http://localhost:8080

echo 1. 測試基本 Redis 連接...
curl -s "%BASE_URL%/diagnostic/redis/connection" | jq .
echo.

echo 2. 測試 Redis PubSub 功能...
curl -s "%BASE_URL%/diagnostic/redis/pubsub" | jq .
echo.

echo 3. 測試分散式鎖功能...
curl -s "%BASE_URL%/diagnostic/redis/lock" | jq .
echo.

echo 4. 測試強制解鎖功能...
curl -s "%BASE_URL%/diagnostic/redis/force-unlock" | jq .
echo.

echo 5. 獲取 Redis 連接信息...
curl -s "%BASE_URL%/diagnostic/redis/info" | jq .
echo.

echo 6. 執行完整診斷...
curl -s "%BASE_URL%/diagnostic/redis/full" | jq .
echo.

echo 7. 測試原始鎖示範（修復後）...
curl -s "%BASE_URL%/demo/lock" | jq .
echo.

echo ========================================
echo 測試完成！
echo ========================================
echo.
echo 如果看到以下結果，表示修復成功：
echo - 基本連接測試: success = true
echo - PubSub 測試: success = true （如果 Redis 支援 PubSub）
echo - 分散式鎖測試: success = true
echo - 原始鎖示範: 大部分步驟 success = true
echo.
echo 如果 PubSub 測試失敗，請檢查：
echo 1. Redis 版本是否支援 PubSub
echo 2. Redis ACL 配置是否允許 PubSub 操作
echo 3. 網路連接是否穩定
echo.
pause
